import React, { useState, useEffect, useContext, useCallback, useMemo } from "react";
import { db, functions } from "../../../config/firebase";
import { UserContext } from "../../../context/UserProvider";
import { GraphicDataDevices } from "../../Common";
import { ALL_KINDS, sensorsLorawan } from "../../../constants/globalConst";
import OptimizedGraphicDataDevices from "../../Common/GraphicDataDevices/OptimizedGraphicDataDevices";

/**
 * Ordena un array de dispositivos por nombre de nodo
 * @param {Array} array - Array de dispositivos a ordenar
 * @returns {Array} - Array ordenado alfabéticamente por nodeId
 */
const orderedByName = (array) => {
  return array.sort((a, b) => {
    const nameA = a.item.name.toLowerCase();
    const nameB = b.item.name.toLowerCase();

    if (nameA < nameB) return -1;
    if (nameA > nameB) return 1;
    return 0;
  });
};

/**
 * Componente mejorado para visualización de gráficos
 * Reemplaza al componente Graph original con mejores prácticas
 */
const GraphImproved = () => {
  // Contexto de usuario
  const { usuario, dataMacCan } = useContext(UserContext);

  // Estados para los dispositivos
  const [outDevices, setOutDevices] = useState([]);
  const [inputDevices, setInputDevices] = useState([]);
  const [manualDevices, setManualDevices] = useState([]);
  // Estado para almacenar nombres de sensores manuales
  const [sensorsManualName, setSensorsManualName] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Constantes para dispositivos LoRa
  const arrayLoraNames = useMemo(() => ["TempAmb", "HumAmb", "CO2Amb", "HumSoil", "TempSoil", "EcSoil", "PhSoil"], []);
  const arrayLoraKinds = useMemo(() => ["50", "51", "52", "53", "54", "55", "56"], []);

  /**
   * Obtiene los nombres de los sensores manuales
   * @returns {Promise<Array>} - Array con los nombres de los sensores manuales
   */
  const getNamesOfManualSensors = useCallback(async () => {
    try {
      const getSubCollections = functions.httpsCallable("getSubCollections");
      const listColl = await getSubCollections({
        docPath: `${usuario.username}/manualMeasurements`,
      });

      const sensorNames = listColl.data.collections.map((item) => ({
        uid: item,
        value: item,
        label: item,
      }));

      return sensorNames;
    } catch (error) {
      console.error("Error al obtener nombres de sensores manuales:", error);
      setError("Error al cargar sensores manuales");
      return [];
    }
  }, [usuario.username]);

  /**
   * Obtiene datos de nodos LoRa
   * @returns {Promise<Array|string>} - Array de dispositivos LoRa o mensaje de error
   */
  const getLoraNodesData = useCallback(async () => {
    try {
      const addr = `${usuario.username}/loraDevices/nodes`;
      const data = await db.collection(addr).get();

      // Verificar si la colección está vacía
      if (data.empty) {
        return "No Lora Devices";
      }

      // Si la colección no está vacía, extraer los nombres de los documentos
      const dataConfig = data.docs.map((doc) => doc.id);
      let sensorsUid = [];

      for (let index = 0; index < dataConfig.length; index++) {
        if (dataConfig[index].length > 1) {
          // Procesar nodos LoRaWAN con múltiples sensores
          const sensorsLWAddr = `${usuario.username}/loraDevices/nodes/${dataConfig[index]}/sensors`;
          const dataLoraWan = await db.collection(sensorsLWAddr).doc("registeredSensors").get();
          const cardsNameAddr = `${usuario.username}/loraDevices/nodes/${dataConfig[index]}/namesOfCards`;
          const cardsDocRef = await db.collection(cardsNameAddr).doc("names").get();

          if (dataLoraWan.exists && cardsDocRef.exists) {
            const dataRecordedFromSensors = dataLoraWan.data().sensors;
            const namesOfCards = cardsDocRef.data().allNames;

            for (let index = 0; index < dataRecordedFromSensors.length; index++) {
              const uidObtained = dataRecordedFromSensors[index].uid;
              const nameObtained = dataRecordedFromSensors[index].name;
              const typeOfSensor = dataRecordedFromSensors[index].type;
              const generalName = (typeOfSensor > 20) ? nameObtained.split("-")[0] : nameObtained;
              const uidSeparated = uidObtained.split("@");
              const idSensor = uidSeparated[4];
              const foundObject = sensorsLorawan.find((item) => item.id === String(typeOfSensor));

              if (foundObject) {
                for (let i = 0; i < foundObject.parameters.length; i++) {
                  const outId = foundObject.parameters[i].outId;
                  const paramName = foundObject.parameters[i].name;
                  const id = `${uidSeparated[0]}@${uidSeparated[2]}@${idSensor}@${outId}`;
                  const nameOfCard = namesOfCards.find((element) => element.id === Number(idSensor));
                  const nameToShow = nameOfCard.name + "-" + paramName;
                  const item = { mac: uidSeparated[0], name: nameToShow, nodeId: generalName };
                  const dataToSave = { id: id, item: item, uidBase: uidObtained };
                  sensorsUid.push(dataToSave);
                }
              }
            }
          } 
        // }
        } else {
          // Procesar nodos LoRa simples
          const configAddr = `${usuario.username}/loraDevices/nodes/${dataConfig[index]}/configNode`;
          const data = await db.collection(configAddr).doc("renderData").get();

          if (data.exists) {
            const dataLora = data.data();
            const uid = dataLora.item.uid;
            const generalName = dataLora.item.nodeName;
            const nodeId = dataLora.item.nodeId;
            const arrayDeCadenas = uid.split("@");
            const mac = arrayDeCadenas[0];
            const outId = arrayDeCadenas[2];

            for (let index = 0; index < arrayLoraKinds.length; index++) {
              const id = `${mac}@0@${arrayLoraKinds[index]}@${outId}`;
              const nameToShow = `${generalName}-${arrayLoraNames[index]}`;
              const item = { mac: mac, name: nameToShow, nodeId: nodeId };
              const dataToSave = { id: id, item: item };
              sensorsUid.push(dataToSave);
            }
          }
        }
      }
      // console.log("Esto es sensorsUid:", sensorsUid);
      return orderedByName(sensorsUid);
    } catch (error) {
      console.error("Error accediendo a la colección:", error);
      setError("Error al cargar dispositivos LoRa");
      return "No Lora Devices";
    }
  }, [usuario.username,arrayLoraKinds,arrayLoraNames]);

  /**
   * Clasifica los sensores por tipo y actualiza los estados correspondientes
   * @param {Array} measure - Array de sensores a clasificar
   * @param {Array} manualNames - Nombres de sensores manuales
   */
  const typeSensor = useCallback((measure, manualNames = []) => {
    let input = [];
    let output = [];
    let uidSplited = [];

    for (let s of measure) {
      uidSplited = s.id.split("@");

      // Ignorar relojes con MAC corta
      if (uidSplited[2] === ALL_KINDS.IN_RELOJ && uidSplited[0].length < 16) {
        // No hacer nada
      }
      // Clasificar salidas
      else if (
        uidSplited[0].length < 16 &&
        (uidSplited[2] === ALL_KINDS.OUT_AC ||
         uidSplited[2] === ALL_KINDS.OUT_PUMB_DC ||
         uidSplited[2] === ALL_KINDS.OUT_PWM)
      ) {
        output.push({ uid: s.id, label: s.item.name, value: s.item.name });
      }
      // Clasificar entradas
      else {
        if(uidSplited[1].length > 1) {
          input.push({ uid: s.id, label: s.item.name, value: s.item.nodeId, uidBase: s.uidBase });
        } else {
          input.push({ uid: s.id, label: s.item.name, value: s.item.name });
        }
      }
    }

    setOutDevices(output);
    setInputDevices(input);
    setManualDevices(manualNames);
  }, []);

  /**
   * Obtiene datos de configuración de dispositivos
   */
  const fetchDeviceData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      let dataConfiguracion = [];
      let localDate = JSON.parse(localStorage.getItem("localDate")) || null;
      const shouldUpdateData = !localDate ||
                              (Date.now() - new Date(localDate.date) >= 900000 && !localDate.flgGraph) ||
                              localDate.flgGraph === null ||
                              localDate.flgGraph === undefined;

      // Si hay datos en caché y son recientes, usarlos
      if (!shouldUpdateData) {
        const cachedData = JSON.parse(localStorage.getItem("dataConfiguracion")) || [];
        const cachedSensorsNames = JSON.parse(localStorage.getItem("sensorsNamesClone")) || [];

        setSensorsManualName(cachedSensorsNames);
        typeSensor(cachedData, cachedSensorsNames);

        // Actualizar flag para próxima carga
        localStorage.setItem(
          "localDate",
          JSON.stringify({
            ...localDate,
            flgGraph: false,
          })
        );

        setIsLoading(false);
        return;
      }

      // Si no hay datos en caché o son antiguos, obtenerlos de la BD
      if (dataMacCan.length > 0) {
        // Obtener datos de dispositivos por MAC y CAN
        await Promise.all(
          dataMacCan.map(async (itemMac) => {
            const dataCan = itemMac.cans.map((Item) => Item.id);
            await Promise.all(
              dataCan.map(async (itemCan) => {
                const addr = `${usuario.username}/infoDevices/${itemMac.mac}/${itemCan}/configModule`;
                const data = await db.collection(addr).get();
                const dataConfig = data.docs.map((doc) => ({
                  id: doc.id,
                  ...doc.data(),
                }));
                dataConfiguracion = [...dataConfiguracion, ...dataConfig];
              })
            );
          })
        );

        // Obtener nombres de sensores manuales
        const sensorsNamesClone = await getNamesOfManualSensors();
        setSensorsManualName(sensorsNamesClone);
        localStorage.setItem("sensorsNamesClone", JSON.stringify(sensorsNamesClone));

        // Obtener datos de dispositivos LoRa
        const dataLora = await getLoraNodesData();
        if (dataLora !== "No Lora Devices") {
          dataConfiguracion = [...dataConfiguracion, ...dataLora];
        }

        // Procesar y guardar datos
        if (dataConfiguracion.length > 0) {
          typeSensor(dataConfiguracion, sensorsNamesClone);
          localStorage.setItem("dataConfiguracion", JSON.stringify(dataConfiguracion));
        }

        // Actualizar fecha de última carga
        localStorage.setItem(
          "localDate",
          JSON.stringify({
            ...localDate,
            date: new Date().toISOString(),
            flgGraph: true,
          })
        );
      }
    } catch (error) {
      console.error("Error al obtener datos de dispositivos:", error);
      setError("Error al cargar datos de dispositivos");
    } finally {
      setIsLoading(false);
    }
  }, [usuario, dataMacCan, typeSensor, getNamesOfManualSensors, getLoraNodesData]);

  /**
   * Obtiene datos para gráficos desde la base de datos
   * @param {string} user - Nombre de usuario
   * @param {string} uid - UID del dispositivo
   * @param {string} fIni - Fecha inicial
   * @param {string} fFin - Fecha final
   * @param {string} tipo - Tipo de módulo (modulo o manual)
   * @returns {Promise<Array>} - Array con los datos para el gráfico
   */
  const getDataFromDB = async (user, uid, fIni, fFin, tipo = "modulo") => {
    try {
      let valArray = [];

      if (tipo === "modulo") {
        const getDataSetValues = functions.httpsCallable("getDataSetValues");
        const values = await getDataSetValues({
          user: user,
          uid: uid.trim(),
          dateStart: fIni,
          dateFinish: fFin,
        });

        const data = values.data.result[0];
        for (let i in data) {
          let newEntry = [Date.parse(data[i].timestamp.value), data[i].val];
          valArray.push(newEntry);
        }
      } else if (tipo === "manual") {
        const getDataSetValues = functions.httpsCallable("getDataSetValuesBinacle");
        valArray = await getDataSetValues({
          user: user,
          uid: uid.trim(),
          dateStart: fIni,
          dateFinish: fFin,
        });
      }

      return valArray;
    } catch (error) {
      console.error("Error al obtener datos para gráfico:", error);
      return [];
    }
  };

  // Efecto para cargar datos al montar el componente o cuando cambian usuario o dataMacCan
  useEffect(() => {
    // Limpiar datos anteriores al cambiar de usuario
    if (usuario && usuario.username) {
      fetchDeviceData();
    }

    // Cleanup function para evitar memory leaks
    return () => {
      // Limpiar estados si es necesario
    };
  }, [usuario, dataMacCan, fetchDeviceData]);

  // Para depuración
  // useEffect(() => {
  //   console.log('Sensores manuales actualizados:', sensorsManualName);
  // }, [sensorsManualName]);

  return (
    <div>
      {isLoading && <div>Cargando dispositivos...</div>}
      {error && <div>Error: {error}</div>}
      <OptimizedGraphicDataDevices
        inputDevices={inputDevices}
        outDevices={outDevices}
        manualDevices={manualDevices}
        getDataFromDB={getDataFromDB}
      />
    </div>
  );
};

export default GraphImproved;
export { GraphImproved };
