# Funcionalidad de Paro Total de Emergencia - Sistema de Tanques de Agua

## Descripción General

Se ha implementado una funcionalidad de **paro total de emergencia** que permite detener todas las operaciones activas de tanques de agua cuando se recibe una señal específica desde Firebase.

## Parámetros de Activación

La funcionalidad se activa cuando se detecta una actualización en el documento de Firebase con los siguientes parámetros:

```javascript
{
  accion: 244,
  act: "recOK", 
  kind: 4,
  val: 0
}
```

## Comportamiento del Sistema

### 1. Detección del Paro Total
- **WaterTankMonitor.jsx** tiene un listener global que monitorea el documento `configOK` en Firebase
- Cuando se detectan los parámetros de paro total, se ejecuta la función `handleEmergencyStop()`

### 2. Acciones Ejecutadas

#### En WaterTankMonitor.jsx:
- Detiene **TODAS** las operaciones activas (llenado, vaciado, recirculación)
- Actualiza el `currentStatus` de todos los tanques a `{ isRunning: false, typeOfAction: null }`
- Actualiza `lastExecution` con timestamp del paro
- Limpia todos los timeouts activos
- Limpia todos los listeners de switches y Firebase específicos de cada tanque
- Resetea la lista de tanques monitoreados
- Emite evento personalizado `waterTankEmergencyStop`
- Envía notificación global
- **NUEVO**: Espera 5 segundos y limpia el documento de Firebase con `{accion: 0, act: "-"}` para evitar ciclos infinitos

#### En WaterTankCollapsibleTable.jsx:
- Escucha el evento `waterTankEmergencyStop`
- Resetea todos los estados de botones:
  - `executingTankId` → `null`
  - `recirculatingTankIds` → `Set()` vacío
- Muestra alerta informativa por 8 segundos
- Todos los botones vuelven al estado "Ejecutar"

#### En WaterTankForm.jsx:
- Escucha el evento `waterTankEmergencyStop`
- Actualiza el estado local de todos los tanques
- Muestra notificación de advertencia por 10 segundos

## Flujo de Ejecución

```mermaid
sequenceDiagram
    participant FB as Firebase
    participant WTM as WaterTankMonitor
    participant WTC as WaterTankCollapsibleTable
    participant WTF as WaterTankForm
    participant UI as Usuario

    FB->>WTM: Actualización documento (kind: 4, val: 0)
    WTM->>WTM: handleEmergencyStop()
    WTM->>FB: Actualizar currentStatus todos los tanques
    WTM->>FB: Actualizar lastExecution todos los tanques
    WTM->>WTM: Limpiar timeouts y listeners
    WTM->>WTC: Evento 'waterTankEmergencyStop'
    WTM->>WTF: Evento 'waterTankEmergencyStop'
    WTC->>WTC: Resetear estados botones
    WTC->>UI: Mostrar alerta (8s)
    WTF->>WTF: Actualizar estado local tanques
    WTF->>UI: Mostrar notificación (10s)
    Note over WTM: Esperar 5 segundos
    WTM->>FB: Limpiar documento (accion: 0, act: "-")
```

## Características Importantes

### 1. **Paro Inmediato**
- Todas las operaciones se detienen inmediatamente
- No hay confirmación adicional requerida

### 2. **Limpieza Completa**
- Se limpian todos los recursos (timeouts, listeners)
- Se resetean todos los estados de UI
- Se actualiza la base de datos

### 3. **Notificaciones Múltiples**
- Alerta en la tabla de tanques (8 segundos)
- Notificación en WaterTankForm (10 segundos)
- Notificación global del sistema de monitoreo

### 4. **Sincronización de Estados**
- Todos los componentes se sincronizan automáticamente
- Los botones vuelven al estado "Ejecutar"
- Los datos en Firebase se actualizan correctamente

## Archivos Modificados

1. **WaterTankMonitor.jsx**
   - Agregado `useCallback` para `handleEmergencyStop`
   - Agregado listener global para `kind: 4`
   - Envueltas funciones de Firebase en `useCallback`

2. **WaterTankCollapsibleTable.jsx**
   - Agregado listener para evento `waterTankEmergencyStop`
   - Reseteo automático de estados de botones

3. **WaterTankForm.jsx**
   - Agregado listener para evento `waterTankEmergencyStop`
   - Actualización de estado local de tanques
   - Notificación al usuario

## Solución al Problema del Ciclo Infinito

### Problema Identificado
Anteriormente, después de detectar el paro de emergencia, el documento de Firebase no se limpiaba, causando que el listener siguiera detectando los mismos parámetros `{accion: 244, act: "recOK", kind: 4, val: 0}` en un ciclo infinito.

### Solución Implementada
Se agregó un `setTimeout` de 5 segundos en la función `handleEmergencyStop` que:
1. Espera a que todas las operaciones de paro se completen
2. Limpia el documento de Firebase actualizando con `{accion: 0, act: "-"}`
3. Evita que el listener siga detectando el paro de emergencia
4. Incluye manejo de errores con logs detallados

```javascript
setTimeout(async () => {
  try {
    console.log(`[WaterTankMonitor] Limpiando documento de paro de emergencia después de 5 segundos`);
    const docPath = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/fromModule/configOK`;
    const docRef = db.doc(docPath);

    await docRef.update({
      accion: 0,
      act: "-"
    });

    console.log(`[WaterTankMonitor] Documento de paro de emergencia limpiado exitosamente`);
  } catch (error) {
    console.error(`[WaterTankMonitor] Error al limpiar documento de paro de emergencia:`, error);
  }
}, 5000);
```

## Pruebas Recomendadas

1. **Prueba con un tanque activo:**
   - Iniciar operación de llenado
   - Simular paro total desde Firebase
   - Verificar que el botón vuelve a "Ejecutar"

2. **Prueba del ciclo infinito (SOLUCIONADO):**
   - Simular paro total desde Firebase
   - Verificar que después de 5 segundos el documento se limpia
   - Confirmar que no se siguen mostrando alertas de paro de emergencia
   - Verificar en los logs que aparece el mensaje de limpieza exitosa

2. **Prueba con múltiples tanques:**
   - Iniciar varias operaciones simultáneas
   - Simular paro total
   - Verificar que todos los botones se resetean

3. **Prueba de notificaciones:**
   - Verificar que aparecen las alertas correspondientes
   - Verificar duración de las notificaciones

## Consideraciones de Seguridad

- El paro total es irreversible una vez activado
- No requiere confirmación adicional para máxima velocidad de respuesta
- Todas las operaciones se detienen sin excepción
- Los datos se guardan en Firebase para auditoría
