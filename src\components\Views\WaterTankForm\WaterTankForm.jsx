import React, { useContext, useState, useEffect, useCallback, useRef } from 'react';
import { UserContext } from '../../../context/UserProvider';
import {
  Divider,
  Grid,
  Button,
  makeStyles,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import { withRouter } from 'react-router-dom/cjs/react-router-dom.min';
import { ConfirmationAlerts } from '../IrrigationForm/components/ConfirmationAlerts';
import WaterTankCollapsibleTable from './WaterTankCollapsibleTable';
import AddWaterTankDialog from './AddWaterTankDialog';
import { db } from '../../../config/firebase';
import { Alert } from '@material-ui/lab';
// moment-timezone is used in WaterTankCollapsibleTable for formatting dates


const useStyles = makeStyles((theme) => ({
  addButton: {
    margin: theme.spacing(2),
  },
  headerContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  title: {
    flexGrow: 1,
  },
}));

const WaterTankForm = () => {
  const classes = useStyles();
  const { currentMac, canIdIrrigation, usuario, togglesNames, togglesUid,
    switchesUid, levelSensorsUid, levelSensorsNames } = useContext(UserContext);

  // Estados para alertas y datos
  const [open, setOpen] = useState(false);
  const [openNotification, setOpenNotification] = useState(false)
  const [typeAlert, setTypeAlert] = useState("info");
  const [typeNotification, setTypeNotification] = useState("info")
  const [alertMessage, setAlertMessage] = useState("");
  const [notificationMessage, setNotificationMessage] = useState("");
  const [alertTitle, setAlertTitle] = useState("");
  const [notificationTitle, setNotificationTitle] = useState("");
  const [waterTanks, setWaterTanks] = useState([]);
  const [showTable, setShowTable] = useState(false);

  // Estados para el diálogo de agregar/editar tanque
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingTank, setEditingTank] = useState(null);

  // Estados para el diálogo de confirmación de eliminación
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [tankToDelete, setTankToDelete] = useState(null);

  // Estados para validación externa MQTT
  const [waitingForConfirmation, setWaitingForConfirmation] = useState(false);
  const [mqttValidationAlert, setMqttValidationAlert] = useState({ show: false, type: '', message: '' });
  const retryCountRef = useRef(0);
  const timeoutRef    = useRef(null);
  const currentCmdRef = useRef(null);
  const unsubscribeRef = useRef(null);


  const handleAlert = useCallback((openValue, type, title = "", message = "") => {
    setOpen(openValue);
    setTypeAlert(type);

    if (type === "success") {
      setAlertTitle(title || "¡Éxito!");
      setAlertMessage(message || "La operación se ha realizado con éxito");
    } else if (type === "error") {
      setAlertTitle(title || "Error");
      setAlertMessage(message || "Ha ocurrido un error al realizar la operación");
    } else if (type === "warning") {
      setAlertTitle(title || "Advertencia");
      setAlertMessage(message || "Tenga precaución al realizar esta operación");
    } else if (type === "info") {
      setAlertTitle(title || "Información");
      setAlertMessage(message || "Operación en proceso");
    }
  }, [setOpen, setTypeAlert, setAlertTitle, setAlertMessage]);

  const handleNotification = useCallback((openValue, type, title = "", message = "") => {
    setOpenNotification(openValue);
    setTypeNotification(type);

    if (type === "success") {
      setNotificationTitle(title || "¡Éxito!");
      setNotificationMessage(message || "La operación se ha realizado con éxito");
    } else if (type === "error") {
      setNotificationTitle(title || "Error");
      setNotificationMessage(message || "Ha ocurrido un error al realizar la operación");
    } else if (type === "warning") {
      setNotificationTitle(title || "Advertencia");
      setNotificationMessage(message || "Tenga precaución al realizar esta operación");
    } else if (type === "info") {
      setNotificationTitle(title || "Información");
      setNotificationMessage(message || "Operación en proceso");
    }
  }, [setOpenNotification, setTypeNotification, setNotificationTitle, setNotificationMessage]);

  // Cargar datos de los tanques de agua
  useEffect(() => {
    const fetchWaterTanks = async () => {
      if (usuario && usuario.username && currentMac && canIdIrrigation) {
        try {
          // Aquí se obtendría la información de los tanques desde Firebase
          // console.log("Esto es togglesNames:", togglesNames)
          // console.log("Esto es togglesUid:", togglesUid)
          const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
          const waterDOcRef = db.collection(waterTankAddr).doc("tankRows");
          const snapshot = await waterDOcRef.get();
          let mockTanks = [];
          if(snapshot.exists) {
            const data = snapshot.data();
            const rowData = data.allRows;

            const tanksData = rowData.map((row, index) => {
              const fillData = row.fill
              const emptyData = row.empty
              const indexFPump = togglesUid.findIndex(uid => uid === fillData.pump)
              const indexEPump = togglesUid.findIndex(uid => uid === emptyData.pump)
              const waterSourceIndex = togglesUid.findIndex(uid => uid === fillData.waterSource);
              const targetTankIndex = togglesUid.findIndex(uid => uid === emptyData.targetTank);
              const fillingPump = togglesNames[indexFPump]
              const emptyingPump = togglesNames[indexEPump]
              const waterSource = waterSourceIndex !== -1 ? togglesNames[waterSourceIndex] : "No encontrado";
              const targetTank = targetTankIndex !== -1 ? togglesNames[targetTankIndex] : "No encontrado";

              // Obtener el estado actual del tanque desde Firebase
              // Estructura esperada: { isRunning: true/false, typeOfAction: "fill"/"empty"/"recirculate" }
              const currentStatus = row.currentStatus || { isRunning: false, typeOfAction: null };

              return {
              id: index,
              name: row.name,
              action: row.lastAction,
              lastExecution: row.lastExecution,
              waterLevelSensor: row.levelSensor,
              recirculationValve: row.recirculationValve,
              recirculationPump: row.recirculationPump,
              currentStatus: currentStatus, // Agregar el estado actual
              details: {
                "Ultima acción": row.lastAction === "fill" ? "Llenado" : "Vaciado",
                "Bomba de llenado": fillingPump,
                "Bomba de vaciado": emptyingPump,
                "Fuente de agua": waterSource,
                "Tanque de destino": targetTank,
                "Tiempo de espera(vaciado)": `${emptyData.minutesWaiting} min`,
                "Tiempo de vaciado": emptyData.minutes + " min " + emptyData.seconds + " seg",
                "Tiempo de llenado": fillData.minutes + " min " + fillData.seconds + " seg",
              },
              fillData: fillData,
              emptyData: emptyData
              }
            });
            mockTanks = tanksData;
          }

          setWaterTanks(mockTanks);
          setShowTable(true);
        } catch (error) {
          console.error("Error al cargar los tanques de agua:", error);
          handleAlert(true, "error", "Error", "No se pudieron cargar los datos de los tanques");
        }
      }
    };

    fetchWaterTanks();
  }, [usuario, currentMac, canIdIrrigation, handleAlert,togglesNames,togglesUid]);

  // useEffect para escuchar el evento de paro total de emergencia
  useEffect(() => {
    const handleEmergencyStop = (event) => {
      const { stoppedTanks, reason, completionTime } = event.detail;

      console.log(`[WaterTankForm] Paro total detectado - Actualizando estados locales`);

      // Actualizar el estado local de todos los tanques para reflejar que no están ejecutándose
      setWaterTanks(prevTanks =>
        prevTanks.map(tank => ({
          ...tank,
          currentStatus: { isRunning: false, typeOfAction: null }
        }))
      );

      // Mostrar notificación sobre el paro total
      const stoppedCount = stoppedTanks.length;
      const message = stoppedCount > 0
        ? `Se han detenido ${stoppedCount} operación${stoppedCount > 1 ? 'es' : ''} de tanque${stoppedCount > 1 ? 's' : ''} debido a un paro total de emergencia.`
        : "Se ha ejecutado un paro total de emergencia en el sistema de tanques.";

      handleNotification(true, "warning", "Paro Total de Emergencia", message);

      // Ocultar notificación después de 10 segundos
      setTimeout(() => {
        handleNotification(false, "info", "", "");
      }, 10000);
    };

    window.addEventListener('waterTankEmergencyStop', handleEmergencyStop);

    return () => {
      window.removeEventListener('waterTankEmergencyStop', handleEmergencyStop);
    };
  }, [handleNotification]);

  // Función para obtener el nivel actual de agua de un tanque
  const getWaterLevelData = async (sensorUid) => {
    if (!sensorUid) {
      console.log("No hay sensor de nivel configurado para este tanque");
      return null;
    }

    try {
      // Extraer el nodeId y sensorId del UID del sensor (formato: nodeId@sensorId)
      const sensorParts = sensorUid.split('@');
      if (sensorParts.length < 4) {
        console.error("Formato de UID de sensor inválido:", sensorUid);
        return null;
      }

      const nodeId = sensorParts[2];
      const sensorId = sensorParts[4];

      // Obtener los datos del sensor
      const docPath = `${usuario.username}/loraDevices/nodes/${nodeId}/sensors/dataSensors`;
      const docRef = await db.doc(docPath).get();

      if (!docRef.exists) {
        console.error("No se encontraron datos para el sensor:", sensorUid);
        return null;
      }

      const sensorData = docRef.data();
      if (!sensorData || !sensorData.data) {
        console.error("Estructura de datos inválida para el sensor:", sensorUid);
        return null;
      }

      // Buscar el sensor específico en el array de datos
      const sensorInfo = sensorData.data.find(sensor => sensor.id === Number(sensorId));
      if (!sensorInfo) {
        console.error("No se encontró información para el sensor ID:", sensorId);
        return null;
      }

      // Determinar el valor a usar según el tipo de medición
      let waterLevelPercentage;
      let waterLevelLiters;
      const currentDate = new Date();
      const sensorDate = new Date(sensorInfo.lastUpdate);
      // diferencia absoluta en milisegundos
      const diffMs = Math.abs(currentDate - sensorDate);
      // a minutos
      const diffMin = diffMs / 1000 / 60;
      if(diffMin <= 15) {
        waterLevelPercentage = sensorInfo.percentageWater;
        waterLevelLiters = sensorInfo.litersWater;
      } else {
        waterLevelPercentage = -1;
        waterLevelLiters = -1;
      }

      // return { percentage: waterLevelPercentage, liters: waterLevelLiters};
      return { percentage: sensorInfo.percentageWater, liters: sensorInfo.litersWater};
    } catch (error) {
      console.error("Error al obtener datos del nivel de agua:", error);
      return null;
    }
  };

  const getContainerWaterVolume = async (containerUid) => {
    if(!containerUid) {
      console.log("No hay datso del volumen del tanque");
      return null;
    }
    try {
      // Extraer el nodeId y sensorId del UID del sensor (formato: nodeId@sensorId)
      const sensorParts = containerUid.split('@');
      if (sensorParts.length < 4) {
        console.error("Formato de UID de sensor inválido:", containerUid);
        return null;
      }
      const nodeId = sensorParts[2];
      const sensorId = sensorParts[4];

      // Obtener los datos del sensor
      const docPath = `${usuario.username}/loraDevices/nodes/${nodeId}/waterContainers/waterTankData`;
      const docRef = await db.doc(docPath).get();
      if(docRef.exists) {
        const sensorData = docRef.data().data;
        const containerData = sensorData.find(container => container.id === sensorId);
        if(containerData) {
          return containerData.capacity;
        } else {
          return null;
        }
      }

    } catch (error) {
      console.error("Error en obtener los datos del volumen del tanque:",error)
    }
  }

  // Función para obtener el estado actual de un switch específico
  const getSwitchState = async (switchUid) => {
    if (!switchUid || !usuario || !usuario.username) {
      console.log("No hay switch configurado o usuario no disponible");
      return null;
    }

    try {
      // Extraer el nodeId, canId y outId del UID del switch (formato: macId@canId@kind@outId)
      const switchParts = switchUid.split('@');
      if (switchParts.length < 4) {
        console.error("Formato de UID de switch inválido:", switchUid);
        return null;
      }

      const macId = switchParts[0];
      const canId = switchParts[1];
      const outId = switchParts[3];

      // Obtener los datos del switch desde Firebase
      const docPath = `${usuario.username}/infoDevices/${macId}/${canId}/fromModule/render`;
      const docRef = await db.doc(docPath).get();

      if (!docRef.exists) {
        console.error("No se encontraron datos para el switch:", switchUid);
        return null;
      }

      const switchData = docRef.data().C_bool;
      const resp = switchData[Number(outId)];

      return resp;
    } catch (error) {
      console.error("Error al obtener el estado del switch:", error);
      return null;
    }
  };

  // Función para calcular el tiempo estimado basado en el nivel de agua
  const calculateEstimatedTime = (waterLevel, totalMinutes, totalSeconds, action) => {
    if (!waterLevel) return null;

    // Convertir el tiempo total a segundos
    const totalTimeInSeconds = (parseInt(totalMinutes) || 0) * 60 + (parseInt(totalSeconds) || 0);
    if (totalTimeInSeconds <= 0) return null;

    // Calcular el tiempo estimado basado en el porcentaje de agua
    const percentage = waterLevel;
    let remainingPercentage;

    if (action === "fill") {
      // Si estamos llenando, el tiempo restante es proporcional al porcentaje que falta llenar
      remainingPercentage = 100 - percentage;
    } else {
      // Si estamos vaciando, el tiempo restante es proporcional al porcentaje actual
      remainingPercentage = percentage;
    }

    // Calcular el tiempo estimado en segundos
    const estimatedTimeInSeconds = (totalTimeInSeconds * remainingPercentage) / 100;

    // Convertir a minutos y segundos
    const estimatedMinutes = Math.floor(estimatedTimeInSeconds / 60);
    const estimatedSeconds = Math.floor(estimatedTimeInSeconds % 60);

    return {
      minutes: estimatedMinutes,
      seconds: estimatedSeconds,
      totalSeconds: estimatedTimeInSeconds
    };
  };

  // Función para enviar la rutina de los tanques por MQTT
  const sendingTankRoutine = async (e, n) => {
    const item = {
        msMqtt: e,
        mac: currentMac,
        action: "Tank Routine",
        fecha: Date.now(),
        uid: n
    }

    try {
        const addr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/sendConfigModule`
        await db.collection(addr).doc("sendConfig").set({ item })
        //console.log("Esto es macId:", macId)
    } catch (error) {
        console.log(error)
    }
  }

  // Función para mapear acciones a números
  const getActionKind = (action) => {
    switch (action) {
      case "fill":
        return 1;
      case "empty":
        return 0;
      case "recirculate":
        return 3;
      case "stop":
        return 2;
      default:
        return 1;
    }
  };
  

  // 1) Función genérica para suscribirse a la confirmación en Firestore
  const subscribeToConfirmation = (commandData) => {
    const addr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/fromModule`;
    const docRef = db.collection(addr).doc("configOK");

    // Cancelar cualquier suscripción previa
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
    }

    unsubscribeRef.current = docRef.onSnapshot(snapshot => {
      const data = snapshot.data();
      // console.log("Esto es commandData:", commandData);

      // Usar commandData del parámetro o como respaldo currentCmdRef.current
      let activeCommandData = commandData;

      // Si commandData es null/undefined, intentar usar currentCmdRef.current como respaldo
      if (!activeCommandData || typeof activeCommandData !== 'object') {
        console.warn("commandData es null, undefined o no es un objeto válido, intentando usar currentCmdRef.current como respaldo");
        activeCommandData = currentCmdRef.current;
      }

      // Verificar que tenemos datos válidos para proceder
      if (!activeCommandData || typeof activeCommandData !== 'object') {
        console.warn("No hay datos de comando válidos disponibles, cancelando validación");
        return;
      }

      // Verificar que activeCommandData tiene las propiedades requeridas
      if (activeCommandData.action === undefined || activeCommandData.expectedVal === undefined || activeCommandData.tankId === undefined) {
        console.warn("Los datos de comando no tienen las propiedades requeridas:", activeCommandData);
        return;
      }

      // Aquí la comparación exacta que tú indicas:
      if (
        data &&
        data.accion === 244 &&
        data.act === 'recOK' &&
        data.kind === activeCommandData.action &&
        data.val === activeCommandData.expectedVal &&
        data.outid === activeCommandData.tankId
      ) {
        // Confirmación recibida correctamente
        clearConfirmationTimeout();

        setWaitingForConfirmation(false);
        setMqttValidationAlert({
          show: true,
          type: 'success',
          message: '✅ El módulo confirmó el comando.'
        });

        // AQUÍ ES DONDE AHORA CAMBIAMOS EL ESTADO DEL BOTÓN Y MOSTRAMOS ALERTAS
        handleSuccessfulMqttValidation(activeCommandData);
        setTimeout(setMqttValidationAlert, 4000, { show: false, type: '', message: '' });

        currentCmdRef.current = null;
      }
    });
  };

  // Función para actualizar el currentStatus en Firebase
  const updateCurrentStatusInDB = async (tankId, isRunning, typeOfAction) => {
    try {
      console.log(`Actualizando currentStatus en DB para tanque ${tankId}:`, { isRunning, typeOfAction });
      const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
      const waterDocRef = db.collection(waterTankAddr).doc("tankRows");

      // Obtener los tanques actuales
      const snapshot = await waterDocRef.get();
      if (snapshot.exists) {
        const data = snapshot.data();
        let allRows = [...data.allRows];

        // Actualizar el tanque específico
        allRows = allRows.map((row, index) => {
          if (index === tankId) {
            return {
              ...row,
              currentStatus: {
                isRunning: isRunning,
                typeOfAction: typeOfAction
              }
            };
          }
          return row;
        });

        // Guardar en Firebase
        await waterDocRef.set({ allRows });
        console.log(`CurrentStatus actualizado en DB para tanque ${tankId}:`, { isRunning, typeOfAction });
      }
    } catch (error) {
      console.error("Error al actualizar currentStatus en la base de datos:", error);
    }
  };

  // 2) Función para manejar la validación exitosa del comando MQTT
  const handleSuccessfulMqttValidation = async (commandData) => {
    // Verificar que commandData existe y tiene las propiedades necesarias
    if (!commandData || typeof commandData !== 'object') {
      console.error("commandData es null, undefined o no es un objeto válido en handleSuccessfulMqttValidation:", commandData);
      return;
    }

    const { tankId, isExecuting } = commandData;

    // Verificar que tankId existe
    if (tankId === undefined || tankId === null) {
      console.error("tankId es undefined o null en commandData:", commandData);
      return;
    }

    // Encontrar el tanque correspondiente
    const tank = waterTanks.find(t => t.id === tankId);
    if (!tank) {
      console.error("No se encontró el tanque con ID:", tankId);
      return;
    }

    // Actualizar el currentStatus en Firebase después de la confirmación exitosa del comando MQTT
    if (!isExecuting) {
      // Si estamos iniciando la operación (isExecuting = false significa que estamos cambiando de no ejecutar a ejecutar)
      await updateCurrentStatusInDB(tankId, true, tank.action);

      // Actualizar también el estado local
      setWaterTanks(prevTanks =>
        prevTanks.map(t =>
          t.id === tankId
            ? { ...t, currentStatus: { isRunning: true, typeOfAction: tank.action } }
            : t
        )
      );
    } else {
      // Si estamos deteniendo la operación (isExecuting = true significa que estamos cambiando de ejecutar a no ejecutar)
      await updateCurrentStatusInDB(tankId, false, null);

      // Actualizar también el estado local
      setWaterTanks(prevTanks =>
        prevTanks.map(t =>
          t.id === tankId
            ? { ...t, currentStatus: { isRunning: false, typeOfAction: null } }
            : t
        )
      );
    }

    // Cambiar el estado del botón en WaterTankCollapsibleTable
    // Esto se hace a través de un evento personalizado
    const event = new CustomEvent('mqttValidationSuccess', {
      detail: { tankId, isExecuting, tank }
    });
    window.dispatchEvent(event);

    // Mostrar las alertas de información correspondientes
    if (!isExecuting) {
      // Si estamos iniciando la operación, obtener información del nivel de agua
      let waterLevelInfo = '';
      let timeInfo = '';

      if (tank.waterLevelSensor) {
        try {
          const waterLevel = await getWaterLevelData(tank.waterLevelSensor);
          // if (waterLevel && waterLevel.percentage !== -1) { //descomentar validacion de sensores de nivel
            waterLevelInfo = `\nNivel actual: ${waterLevel.percentage}%.`;

            // Calcular tiempo estimado
            const actionData = tank.action === "fill" ? tank.fillData : (tank.action === "empty" ? tank.emptyData : null);
            if (actionData) {
              const totalMinutes = actionData.minutes || 0;
              const totalSeconds = actionData.seconds || 0;
              const estimated = calculateEstimatedTime(waterLevel.percentage, totalMinutes, totalSeconds, tank.action);
              if (estimated && tank.action === "fill") {
                timeInfo = `\nTiempo estimado de ${estimated.minutes} minuto(s) y ${estimated.seconds} segundo(s).`;
              } else if(estimated && tank.action === "empty") {
                timeInfo = `con una duración estimada de ${estimated.minutes} minuto(s) y ${estimated.seconds} segundo(s).`;
              }
            }
          // }
        } catch (error) {
          console.error("Error al obtener nivel de agua para alerta:", error);
        }
      }

      const actionText = tank.action === "fill" ? "llenado" : (tank.action === "empty" ? "vaciado" : "recirculación");
      const tankActionData = tank.action === "fill" ? tank.fillData : (tank.action === "empty" ? tank.emptyData : null);
      const alertTitle = (tank.action === "fill" || tank.action === "recirculate") ? 
      "Operación iniciada" : "Inicio el tiempo de espera";

      const alertMessage = (tank.action === "fill" || tank.action === "recirculate") ?
      (tank.action === "fill" ?
      `Se ha iniciado la operación de ${actionText} en ${tank.name}.${waterLevelInfo}${timeInfo}` :
      `Se ha iniciado la operación de ${actionText} en ${tank.name}.${waterLevelInfo}`
      )
      :
      `Se ha iniciado el periodo de espera para finalizar la recirculación en el tanque ${tank.name}.
      Tras esperar ${tankActionData.minutesWaiting} minuto(s), comenzará el proceso de vaciado,${timeInfo}`;

      handleAlert(
        true,
        "info",
        alertTitle,
        alertMessage
      );
      setTimeout(handleAlert, 20000, false, "info", "", "")

      if (tank.action === "empty") {
        handleNotification(
          true,
          "info",
          "Preparación de nuevos Nutrientes",
          `Se recomienda preparar los nuevos nutrientes para el siguiente llenado del tanque ${tank.name}.`
        );
        setTimeout(handleNotification, 20000, false, "info", "", "")
      }
    } else {
      // Si estamos deteniendo la operación, mostrar alerta de detención
      const actionText = tank.action === "fill" ? "llenado" : (tank.action === "empty" ? "vaciado" : "recirculación");
      handleAlert(
        true,
        "warning",
        "Operación detenida",
        `Se ha detenido la operación de ${actionText} en ${tank.name} por acción del usuario.`
      );
      setTimeout(handleAlert, 6000, false, "info", "", "")
    }
  };

  // 3) Función para limpiar timeout y listener
  const clearConfirmationTimeout = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }
  };

  // 4) Envío inicial + listener + timeout
  const sendMqttCommandWithValidation = async (mqttCommand, moduleId, tank, isExecuting = false) => {
    const commandData = {
      action: getActionKind(tank.action),
      expectedVal: isExecuting ? 0 : 1,
      tankId: tank.id,
      mqttCommand,
      moduleId,
      isExecuting // Agregar esta información para saber si estamos iniciando o deteniendo
    };

    currentCmdRef.current = commandData;
    retryCountRef.current = 0;

    setWaitingForConfirmation(true);
    setMqttValidationAlert({
      show: true,
      type: 'info',
      message: '⏳ Esperando respuesta del módulo ambiente...'
    });

    // 4.1 Publicar el comando MQTT
    await sendingTankRoutine(mqttCommand, moduleId);

    // 4.2 Suscribirse al documento de Firebase
    subscribeToConfirmation(commandData);

    // 4.3 Programar el timeout del primer intento
    timeoutRef.current = setTimeout(handleMqttTimeout, 5000);
  };


  // 5) Manejo de reintentos
  const handleMqttTimeout = async () => {
    // Verificar que currentCmdRef.current existe antes de proceder
    if (!currentCmdRef.current) {
      console.warn("currentCmdRef.current es null en handleMqttTimeout, cancelando reintentos");
      clearConfirmationTimeout();
      setWaitingForConfirmation(false);
      return;
    }

    if (retryCountRef.current < 2) {
      retryCountRef.current += 1;
      setMqttValidationAlert({
        show: true,
        type: 'warning',
        message: `🔄 Reintentando envío de comando... (Intento ${retryCountRef.current + 1}/3)`
      });

      const { mqttCommand, moduleId } = currentCmdRef.current;

      // Verificar que los datos necesarios existen
      if (!mqttCommand || !moduleId) {
        console.error("Datos de comando incompletos para reintento:", currentCmdRef.current);
        clearConfirmationTimeout();
        setWaitingForConfirmation(false);
        setMqttValidationAlert({
          show: true,
          type: 'error',
          message: '❌ Error en los datos del comando. Operación cancelada.'
        });
        setTimeout(setMqttValidationAlert, 4000, { show: false, type: '', message: '' });
        currentCmdRef.current = null;
        return;
      }

      // Reenviar comando
      await sendingTankRoutine(mqttCommand, moduleId);

      // Limpiar timeout y listener anterior
      clearConfirmationTimeout();

      // Volver a suscribir listener con la misma lógica
      subscribeToConfirmation(currentCmdRef.current);

      // Programar siguiente timeout
      timeoutRef.current = setTimeout(handleMqttTimeout, 5000);

    } else {
      // Tras 3 intentos, confirmar fallo
      clearConfirmationTimeout();
      setWaitingForConfirmation(false);
      setMqttValidationAlert({
        show: true,
        type: 'error',
        message: '❌ No responde el módulo ambiente. Verifique la conexión.'
      });
      setTimeout(setMqttValidationAlert, 4000, { show: false, type: '', message: '' });
      currentCmdRef.current = null;
    }
  };

  // 6) Cleanup al desmontar el componente
  useEffect(() => {
    return () => {
      clearConfirmationTimeout();
    };
  }, []);

 

  // Función para actualizar la última ejecución en la base de datos
  const updateTankLastExecutionInDB = async (tankId, lastExecutionTime,tankAction) => {
    try {
      // console.log("Entre a waterTankLastExecutionInDB");
      const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
      const waterDocRef = db.collection(waterTankAddr).doc("tankRows");

      // Obtener los tanques actuales
      const snapshot = await waterDocRef.get();
      if (snapshot.exists) {
        const data = snapshot.data();
        let allRows = [...data.allRows];

        // Actualizar el tanque específico
        allRows = allRows.map((row, index) => {
          if (index === tankId) {
            return { ...row, lastExecution: lastExecutionTime, lastAction: tankAction };
          }
          return row;
        });

        // Guardar en Firebase
        await waterDocRef.set({ allRows });
        console.log(`Última ejecución actualizada en DB para tanque ${tankId}:`, lastExecutionTime);
      }
    } catch (error) {
      console.error("Error al actualizar la última ejecución en la base de datos:", error);
    }
  };

  // Manejar la ejecución de una acción en un tanque
  const handleExecuteTank = async (tank, completed = false, stoppedBySwitch = false, completionTime = null) => {
    // Verificamos si el tanque ya está en ejecución (esto lo determinará el componente WaterTankCollapsibleTable)
    // Si el componente WaterTankCollapsibleTable cambia executingTankId a null, significa que estamos deteniendo
    // Si lo cambia al ID del tanque, significa que estamos ejecutando
    const moduleId = currentMac + "@" + canIdIrrigation;

    // Verificamos si hay un tanque en ejecución consultando el estado del botón en la tabla
    const isExecuting = document.querySelector(`button[data-tank-id="${tank.id}"]`)?.textContent === 'Detener';

    // Verificar si la operación se completó automáticamente por el switch
    if (completed && stoppedBySwitch) {
      // Si la operación se completó automáticamente por un cambio en el Switch
      const actionText = tank.action === "fill" ? "llenado" : "vaciado";
      const switchType = tank.action === "fill" ? "máximo" : "mínimo";
      handleAlert(true, "success", "Operación completada", `La operación de ${actionText} en ${tank.name} ha sido completada automáticamente porque se activó el sensor de nivel ${switchType}.`);

      // Actualizar la fecha y hora de finalización en la base de datos
      // Usar el completionTime pasado como parámetro o crear uno nuevo
      const finalCompletionTime = completionTime || new Date().toISOString();
      console.log("Entre a stoppedBySwitch, completionTime:", finalCompletionTime);

      await updateTankLastExecutionInDB(tank.id, finalCompletionTime,tank.action);

      // Actualizar el currentStatus en Firebase cuando se completa por switch
      await updateCurrentStatusInDB(tank.id, false, null);

      // Actualizar también el estado local
      const updatedTanks = waterTanks.map(t => {
        if (t.id === tank.id) {
          return {
            ...t,
            lastExecution: finalCompletionTime,
            currentStatus: { isRunning: false, typeOfAction: null }
          };
        }
        return t;
      });
      setWaterTanks(updatedTanks);

      // Enviar comando MQTT para detener la operación
      let mqtt = "";
      const len = 16;
      const action = 244;
      const typeAction = 2;
      const fillData = tank.fillData;
      const emptyData = tank.emptyData;
      const estimatedMinutes = 0;
      const estimatedSeconds = 0;
      const totalMinutes = tank.action === "fill" ? fillData.minutes : emptyData.minutes;
      const totalSeconds = tank.action === "fill" ? fillData.seconds : emptyData.seconds;
      const minutesWaiting = tank.action === "fill" ? 0 : emptyData.minutesWaiting;

      const rowIndex = tank.id;
      const recirculationValve = togglesUid.findIndex(uid => uid === tank.recirculationValve);
      const fillingPump = togglesUid.findIndex(uid => uid === fillData.pump);
      const recirculationPump = togglesUid.findIndex(uid => uid === tank.recirculationPump);
      const fillingValve = togglesUid.findIndex(uid => uid === fillData.fillValve);
      const fillingWaterSourceIndex = togglesUid.findIndex(uid => uid === fillData.waterSource);
      const emptyValve = togglesUid.findIndex(uid => uid === emptyData.emptyValve);
      const emptyPumpIndex = togglesUid.findIndex(uid => uid === emptyData.pump);
      const switchIndex = switchesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.maxSwitch : emptyData.minSwitch));

      mqtt =
          len +
          "," +
          action +
          "," +
          canIdIrrigation +
          "," +
          typeAction +
          "," +
          rowIndex +
          "," +
          switchIndex +
          "," +
          estimatedMinutes +
          "," +
          estimatedSeconds +
          "," +
          totalMinutes +
          "," +
          totalSeconds +
          "," +
          minutesWaiting +
          "," +
          fillingPump +
          "," +
          emptyPumpIndex +
          "," +
          recirculationPump +
          "," +
          fillingWaterSourceIndex +
          "," +
          recirculationValve +
          "," +
          fillingValve +
          "," +
          emptyValve;

      // console.log("Esto es mqtt para detener operación completada:", mqtt);
      sendingTankRoutine(mqtt, moduleId);
      setTimeout(handleAlert, 7000, false, "info", "", "")

      return; // Salir de la función después de manejar la finalización automática
    }

    if (isExecuting && !completed) {//Se detiene la accion de llenado/vaciado
      // Si estamos deteniendo la acción y no ha terminado la operación
      // NO MOSTRAR ALERTAS AQUÍ - Se mostrarán después de la validación MQTT exitosa
      // La alerta de detención se mostrará en handleSuccessfulMqttValidation

      let mqtt = "";
      const len = 16;
      const action = 244;
      const typeAction = 2;
      const fillData = tank.fillData;
      const emptyData = tank.emptyData;
      const estimatedMinutes = 0;
      const estimatedSeconds = 0;
      const totalMinutes = tank.action === "fill" ? fillData.minutes : emptyData.minutes;
      const totalSeconds = tank.action === "fill" ? fillData.seconds : emptyData.seconds;
      const minutesWaiting = tank.action === "fill" ? 0 : emptyData.minutesWaiting;

      const rowIndex = tank.id;
      const recirculationValve = togglesUid.findIndex(uid => uid === tank.recirculationValve);
      const fillingPump = togglesUid.findIndex(uid => uid === fillData.pump);
      const recirculationPump = togglesUid.findIndex(uid => uid === tank.recirculationPump);
      const fillingValve = togglesUid.findIndex(uid => uid === fillData.fillValve);
      const fillingWaterSourceIndex = togglesUid.findIndex(uid => uid === fillData.waterSource);
      const emptyValve = togglesUid.findIndex(uid => uid === emptyData.emptyValve);
      const emptyPumpIndex = togglesUid.findIndex(uid => uid === emptyData.pump);
      const switchIndex = switchesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.maxSwitch : emptyData.minSwitch));
      const tankStop = {
        id: tank.id,
        name: tank.name,
        action: "stop",
        lastExecution: tank.lastExecution,
        waterLevelSensor: tank.waterLevelSensor,
        recirculationValve: tank.recirculationValve,
        details: tank.details,
        fillData: tank.fillData,
        emptyData: tank.emptyData
      };
      

      mqtt =
          len +
          "," +
          action +
          "," +
          canIdIrrigation +
          "," +
          typeAction +
          "," +
          rowIndex +
          "," +
          switchIndex +
          "," +
          estimatedMinutes +
          "," +
          estimatedSeconds +
          "," +
          totalMinutes +
          "," +
          totalSeconds +
          "," +
          minutesWaiting +
          "," +
          fillingPump +
          "," +
          emptyPumpIndex +
          "," +
          recirculationPump +
          "," +
          fillingWaterSourceIndex +
          "," +
          recirculationValve +
          "," +
          fillingValve +
          "," +
          emptyValve;
            
        // console.log("Esto es mqtt:", mqtt);
        await sendMqttCommandWithValidation(mqtt, moduleId, tankStop, true);

    } else if (!completed) {//Se ejecuta la accion por primera vez
      // Si estamos iniciando la acción y no ha terminado la operación, primero verificamos el estado de los switches de seguridad
      const fillData = tank.fillData;
      const emptyData = tank.emptyData;
      console.log("Esto es tank:", tank);

      // Obtener los UIDs de los switches de nivel máximo y mínimo
      const maxLevelSwitchUid = fillData.maxSwitch;
      const minLevelSwitchUid = emptyData.minSwitch;

      // Verificar el estado de los switches según la acción a realizar
      if (tank.action === "fill") {
        // Para llenado, verificar que el switch de nivel máximo no esté activado (1)
        if (maxLevelSwitchUid) {
          const maxSwitchState = await getSwitchState(maxLevelSwitchUid);

          if (maxSwitchState === "1") {
            // Si el switch de nivel máximo está activado, mostrar alerta y no ejecutar la acción
            handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el llenado en ${tank.name} porque el sensor de nivel máximo está activado.`
            );
            setTimeout(handleAlert, 6000, false, "info", "", "")
            return; // Salir de la función sin ejecutar la acción
          }
        }
        const waterSourceUid = fillData.waterSourceLevelSensor;
        const waterSourceIndex = levelSensorsUid.findIndex(uid => uid === waterSourceUid);
        const waterSourceName = waterSourceUid !== null ? levelSensorsNames[waterSourceIndex] : null;

        if(waterSourceUid !== null && tank.waterLevelSensor !== null) {
          try {
            const waterLevelSource = await getWaterLevelData(waterSourceUid);
            const waterCapacityFillTank = await getContainerWaterVolume(tank.waterLevelSensor);
            // if(waterLevelSource && waterLevelSource.liters !== -1) { //descomentar validacion de sensores de nivel
              if(waterCapacityFillTank && waterCapacityFillTank !== 0) {
                if (waterLevelSource.liters < waterCapacityFillTank) {
                  handleAlert(
                    true,
                    "warning",
                    "Operación no permitida",
                    `No se puede ejecutar el llenado en ${tank.name} porque el tanque de origen "${waterSourceName}" no tiene suficiente agua.`
                  );
                  setTimeout(handleAlert, 6000, false, "info", "", "")
                  return; // Salir de la función sin ejecutar la acción
                }
              } else {
                handleAlert(
                  true,
                  "warning",
                  "Operación no permitida",
                  `No se puede ejecutar el llenado en ${tank.name} porque se desconoce su volumen máximo.Completa la informacion del tanque en la configuración de la card.`
                );
                setTimeout(handleAlert, 6000, false, "info", "", "")
                return; // Salir de la función sin ejecutar la acción
              }

              //................................................................................................
            // } else {
            //   handleAlert(
            //     true,
            //     "warning",
            //     "Operación no permitida",
            //     `No se puede ejecutar el llenado en ${tank.name}. Revisa el sensor de "${waterSourceName}" y su nodo, no ha reportado en mas de 15 mins.`
            //   );
            //   setTimeout(handleAlert, 6000, false, "info", "", "")
            //   return; // Salir de la función sin ejecutar la acción
            // }
            //................................................................................................
            
          } catch (error) {
            console.error("Error en la verificacion del nivel del tanque de origen:", error);
          }
        }
      } else if (tank.action === "empty") {
        // Para vaciado, verificar que el switch de nivel mínimo no esté desactivado (0)
        if (minLevelSwitchUid) {
          const minSwitchState = await getSwitchState(minLevelSwitchUid);

          if (minSwitchState === "0") {
            // Si el switch de nivel mínimo está desactivado, mostrar alerta y no ejecutar la acción
            handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el vaciado en ${tank.name} porque el sensor de nivel mínimo está desactivado.`
            );
            setTimeout(handleAlert, 6000, false, "info", "", "")
            return; // Salir de la función sin ejecutar la acción
          }
        }

        // Verificar el nivel de agua del tanque de destino (targetTank) para la acción de vaciar
        const targetTankUid = emptyData.targetTankWaterLevelS;
        const targetTankIndex = levelSensorsUid.findIndex(uid => uid === targetTankUid);
        const targetTankName = targetTankUid !== null ? levelSensorsNames[targetTankIndex] : null;
        if (targetTankUid !== null) {
          try {
            // Obtener el nivel actual de agua del tanque de destino
            const targetWaterLevel = await getWaterLevelData(targetTankUid);
            const targetWaterCapacity = await getContainerWaterVolume(targetTankUid);
            const maxCapacityOfTank = await getContainerWaterVolume(tank.waterLevelSensor);
            
            const TotalEstimatedWaterVolume = targetWaterLevel.liters + maxCapacityOfTank;

            // if (targetWaterLevel && targetWaterLevel.liters !== -1 ) { //Descomentar validacion de sensores de nivel
              if(targetWaterCapacity && targetWaterCapacity !== 0) {
                if(maxCapacityOfTank && maxCapacityOfTank !== 0){
                  if(TotalEstimatedWaterVolume > targetWaterCapacity) { //Si el nivel de agua supera a la capcidad del tanque de destino, no se puede ejecutar la acción de vaciado
                    const minimumCapacityToReceiveWater = Number(targetWaterCapacity) - Number(maxCapacityOfTank);
                    const minimumPercentage = (minimumCapacityToReceiveWater * 100) / Number(targetWaterCapacity);
                    handleAlert(
                      true,
                      "warning",
                      "Operación no permitida",
                      `No se puede ejecutar el vaciado en ${tank.name} porque el tanque de destino "${targetTankName}" está demasiado lleno (${targetWaterLevel.percentage}%). El nivel debe ser menor o igual al ${minimumPercentage.toFixed(0)}%.`
                    );
                    setTimeout(handleAlert, 6000, false, "info", "", "")
                    return; // Salir de la función sin ejecutar la acción
                  }

                } else {
                  handleAlert(
                    true,
                    "warning",
                    "Operación no permitida",
                    `No se puede ejecutar el vaciado en ${tank.name} porque se desconoce su volumen máximo. Es necesario configurar los datos en el sensor de nivel en el tanque que se quiere vaciar.`
                  );
                  setTimeout(handleAlert, 6000, false, "info", "", "")
                  return; // Salir de la función sin ejecutar la acción
                }
              } else  {
                handleAlert(
                    true,
                    "warning",
                    "Operación no permitida",
                    `No se puede ejecutar el vaciado en ${tank.name} porque se desconoce el volumen máximo del tanque de destino "${targetTankName}". Es necesario configurar los datos del sensor de nivel en el tanque de destino.`
                  );
                  setTimeout(handleAlert, 6000, false, "info", "", "")
                  return; // Salir de la función sin ejecutar la acción
              }
            
              //....................................................................................................
            // } else if(targetWaterLevel && targetWaterLevel === -1) {
            //   handleAlert(
            //     true,
            //     "warning",
            //     "Operación no permitida",
            //     `No se puede ejecutar el vaciado en ${tank.name}.Revisa el tanque de destino "${targetTankName}" y su nodo, no ha reportado en mas de 15 mins.`
            //   );
            //   setTimeout(handleAlert, 6000, false, "info", "", "")
            //   return; // Salir de la función sin ejecutar la acción
            // }
            //....................................................................................................
          } catch (error) {
            console.error("Error al verificar el nivel del tanque de destino:", error);
            // En caso de error, continuamos con la operación pero registramos el error
          }

        }
      }

      let estimatedMinutes = 0;
      let estimatedSeconds = 0;
      // Si estamos iniciando la acción, primero consultamos el nivel de agua actual
      if (tank.waterLevelSensor) {
        try {
          // Obtener el nivel actual de agua
          const waterLevel = await getWaterLevelData(tank.waterLevelSensor);
          const waterLevelSensorIndex = levelSensorsUid.findIndex(uid => uid === tank.waterLevelSensor);
          const waterLevelSensorName = levelSensorsNames[waterLevelSensorIndex];
          // if (waterLevel && waterLevel.percentage !== -1) {
            // Calcular el tiempo estimado basado en el nivel de agua
            const actionData = tank.action === "fill" ? tank.fillData : (tank.action === "empty" ? tank.emptyData : null);
            const totalMinutes = actionData !== null ? actionData.minutes : 0;
            const totalSeconds = actionData !== null ? actionData.seconds : 0;

            const estimated = (tank.action === "fill" || tank.action === "empty") ? 
            calculateEstimatedTime(waterLevel.percentage, totalMinutes, totalSeconds, tank.action)
            :
            null;
            if (estimated) {
              estimatedMinutes = estimated.minutes;
              estimatedSeconds = estimated.seconds;
            }

            // NO MOSTRAR ALERTAS AQUÍ - Se mostrarán después de la validación MQTT exitosa
            // Las alertas de información se mostrarán en handleSuccessfulMqttValidation
            // después de que se confirme que el comando MQTT fue recibido correctamente
            //....................................................................................
          // } else {
            // Si no se pudo obtener el nivel, mostrar alerta genérica
          //   handleAlert(true, "warning",
          //     "No se pudo enviar la acción",
          //     `La acción ${tank.action} en ${tank.name} no se pudo completar debido a que no se tiene informacion reciente del sensor de nivel ${waterLevelSensorName}. Revisar el sensor y nodo.`);
            
          //   setTimeout(handleAlert, 6000, false, "info", "", "")
          //   return; // Salir de la función sin ejecutar la acción
          // }
          //....................................................................................
        } catch (error) {
          console.error("Error al obtener el nivel de agua:", error);
          handleAlert(true, "error", "No se pudo obtener el nivel de agua", `No se pudo obtener el nivel de agua asociado al tanque ${tank.name}`);
          setTimeout(handleAlert, 6000, false, "info", "", "")
          return; // Salir de la función sin ejecutar la acción
        }
      } else {
        // Si no hay sensor de nivel configurado, mostrar alerta genérica
        handleAlert(true, "warining", "No hay sensor de nivel configurado", `No se puede ejecutar la acción ${tank.action} en ${tank.name}`);
        setTimeout(handleAlert, 6000, false, "info", "", "")
        return; // Salir de la función sin ejecutar la acción
      }

      // Preparar y enviar el comando MQTT
      let mqtt = "";
      const len = 16;
      const action = 244;
      const typeAction = tank.action === "fill" ? 1 : (tank.action === "empty" ? 0 : 3);
      const totalMinutes = tank.action === "fill" ? fillData.minutes : (tank.action === "empty" ? emptyData.minutes : 0);
      const totalSeconds = tank.action === "fill" ? fillData.seconds : (tank.action === "empty" ? emptyData.seconds : 0);
      const minutesWaiting = tank.action === "fill" ? 0 : (tank.action === "empty" ? emptyData.minutesWaiting : 0);

      const rowIndex = tank.id;
      const recirculationValve = togglesUid.findIndex(uid => uid === tank.recirculationValve);
      const fillingPump = togglesUid.findIndex(uid => uid === fillData.pump);
      const recirculationPump = togglesUid.findIndex(uid => uid === tank.recirculationPump);
      const fillingValve = togglesUid.findIndex(uid => uid === fillData.fillValve);
      const fillingWaterSourceIndex = togglesUid.findIndex(uid => uid === fillData.waterSource);
      const emptyValve = togglesUid.findIndex(uid => uid === emptyData.emptyValve);
      const emptyPumpIndex = togglesUid.findIndex(uid => uid === emptyData.pump);
      const switchIndex = switchesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.maxSwitch : emptyData.minSwitch));
            
      
      mqtt =
          len +
          "," +
          action +
          "," +
          canIdIrrigation +
          "," +
          typeAction +
          "," +
          rowIndex +
          "," +
          switchIndex +
          "," +
          estimatedMinutes +
          "," +
          estimatedSeconds +
          "," +
          totalMinutes +
          "," +
          totalSeconds +
          "," +
          minutesWaiting +
          "," +
          fillingPump +
          "," +
          emptyPumpIndex +
          "," +
          recirculationPump +
          "," +
          fillingWaterSourceIndex +
          "," +
          recirculationValve +
          "," +
          fillingValve +
          "," +
          emptyValve;
          
      // console.log("Esto es mqtt:", mqtt);
      await sendMqttCommandWithValidation(mqtt, moduleId, tank, false);

      // Nota: El tiempo de última ejecución se actualizará cuando la operación se complete exitosamente
      // (cuando stoppedBySwitch sea true), no al inicio de la operación
    }
  };

  // Manejar la edición de un tanque
  const handleEditTank = (tank) => {
    // Si el clic viene del selector de acción, solo actualizamos la acción
    if (tank.action) {
      const actionText = tank.action === "fill" ? "Llenar" : (tank.action === "empty" ? "Vaciar" : "Recircular");
      handleAlert(true, "info", "Acción cambiada", `Acción cambiada a ${actionText} para ${tank.name}`);

      setWaterTanks(prevTanks => {
        const updatedTanks = prevTanks.map(t => {
          if (t.id === tank.id) {
            // Solo actualizar la acción, mantener todo lo demás igual
            // Esto evita cambios innecesarios que puedan afectar otros componentes
            return { ...t, action: tank.action };
          }
          return t;
        });
        console.log("Esto es updatedTanks:",updatedTanks)
        return updatedTanks;
      });
      setTimeout(handleAlert, 4000, false, "info", "", "")
    } else {
      // Si el clic viene del botón de editar, abrimos el diálogo de edición
      handleAlert(true, "info", "Edición", `Editando configuración de ${tank.name}`);

      // Buscar el tanque actualizado en el estado actual para asegurar que tenemos la acción más reciente
      const currentTank = waterTanks.find(t => t.id === tank.id) || tank;

      // Establecer el tanque que se está editando y abrir el diálogo
      setEditingTank(currentTank);
      setOpenAddDialog(true);
      console.log("Esto es tank (actualizado):", currentTank)
      setTimeout(handleAlert, 4000, false, "info", "", "")
    }
  };

  // Manejar la eliminación de un tanque
  const handleDeleteTank = (tank) => {
    setTankToDelete(tank);
    setOpenDeleteDialog(true);
  };

  // Cerrar el diálogo de confirmación de eliminación
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setTankToDelete(null);
  };

  // Confirmar y ejecutar la eliminación del tanque
  const handleConfirmDeleteTank = async () => {
    if (!tankToDelete) return;

    try {
      // Eliminar de Firebase
      const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
      const waterDocRef = db.collection(waterTankAddr).doc("tankRows");

      // Obtener los tanques actuales
      const snapshot = await waterDocRef.get();
      if (snapshot.exists) {
        const data = snapshot.data();
        let allRows = [...data.allRows];

        // Eliminar el tanque del array usando el índice (id)
        allRows.splice(tankToDelete.id, 1);

        // Guardar el array actualizado en Firebase
        await waterDocRef.set({ allRows });

        // Actualizar el estado local eliminando el tanque y reindexando
        const updatedTanks = waterTanks
          .filter(tank => tank.id !== tankToDelete.id)
          .map((tank, index) => ({
            ...tank,
            id: index // Reindexar los IDs para mantener la consistencia
          }));

        setWaterTanks(updatedTanks);

        // Mostrar mensaje de éxito
        handleAlert(true, "success", "Tanque eliminado", `El tanque "${tankToDelete.name}" ha sido eliminado correctamente`);
        setTimeout(handleAlert, 4000, false, "info", "", "")
      }
    } catch (error) {
      console.error("Error al eliminar el tanque:", error);
      handleAlert(true, "error", "Error", `No se pudo eliminar el tanque "${tankToDelete.name}": ${error.message}`);
      setTimeout(handleAlert, 4000, false, "info", "", "")
    } finally {
      // Cerrar el diálogo
      handleCloseDeleteDialog();
    }
  };

  // Abrir el diálogo para agregar un nuevo tanque
  const handleOpenAddDialog = () => {
    setOpenAddDialog(true);
  };

  // Cerrar el diálogo para agregar/editar un tanque
  const handleCloseAddDialog = () => {
    setOpenAddDialog(false);
    setEditingTank(null); // Limpiar el tanque en edición
  };

  // Manejar el éxito al agregar o actualizar un tanque
  const handleAddSuccess = (allRows, isEditing = false) => {
    handleCloseAddDialog();

    if (isEditing) {
      handleAlert(true, "success", "Tanque actualizado", "El tanque se ha actualizado correctamente");
      setTimeout(handleAlert, 5000, false, "info", "", "")
    } else {
      handleAlert(true, "success", "Tanque agregado", "El tanque se ha agregado correctamente");
      setTimeout(handleAlert, 5000, false, "info", "", "")
    }

    // Transformar los datos de Firebase al formato esperado por WaterTankCollapsibleTable
    const formattedTanks = allRows.map((row, index) => {
      const fillData = row.fill;
      const emptyData = row.empty;
      const indexFPump = togglesUid.findIndex(uid => uid === fillData.pump);
      const indexEPump = togglesUid.findIndex(uid => uid === emptyData.pump);
      const waterSourceIndex = togglesUid.findIndex(uid => uid === fillData.waterSource);
      const targetTankIndex = togglesUid.findIndex(uid => uid === emptyData.targetTank);
      const fillingPump = indexFPump !== -1 ? togglesNames[indexFPump] : "No encontrado";
      const emptyingPump = indexEPump !== -1 ? togglesNames[indexEPump] : "No encontrado";
      const waterSource = waterSourceIndex !== -1 ? togglesNames[waterSourceIndex] : "No encontrado";
      const targetTank = targetTankIndex !== -1 ? togglesNames[targetTankIndex] : "No encontrado";

      // Obtener el estado actual del tanque desde Firebase
      // Estructura esperada: { isRunning: true/false, typeOfAction: "fill"/"empty"/"recirculate" }
      const currentStatus = row.currentStatus || { isRunning: false, typeOfAction: null };

      return {
        id: index,
        name: row.name,
        action: row.action || row.lastAction,
        lastExecution: row.lastExecution,
        waterLevelSensor: row.levelSensor,
        recirculationPump: row.recirculationPump,
        recirculationValve: row.recirculationValve,
        currentStatus: currentStatus, // Agregar el estado actual
        details: {
          "Ultima acción": row.lastAction === "fill" ? "Llenado" : "Vaciado",
          "Bomba de llenado": fillingPump,
          "Bomba de vaciado": emptyingPump,
          "Fuente de agua": waterSource,
          "Tanque de destino": targetTank,
          "Tiempo de espera(vaciado)": `${emptyData.minutesWaiting} min`,
          "Tiempo de vaciado": emptyData.minutes + " min " + emptyData.seconds + " seg",
          "Tiempo de llenado": fillData.minutes + " min " + fillData.seconds + " seg",
        },
        fillData: fillData,
        emptyData: emptyData
      };
    });

    setWaterTanks(formattedTanks);
    setShowTable(true);

    // Si estamos editando, notificar a los componentes hijos sobre la actualización
    if (isEditing && editingTank) {
      // Disparar evento personalizado para notificar actualización del tanque
      const event = new CustomEvent('tankUpdated', {
        detail: {
          tankId: editingTank.id,
          updatedTank: formattedTanks.find(t => t.id === editingTank.id)
        }
      });
      window.dispatchEvent(event);
    }
  };

  return (
    <>
      <div className={classes.headerContainer}>
        <Grid
          container
          justifyContent="space-between"
          alignItems="center"
        >
          <Grid item className={classes.title}>
            <h2>Control de Tanques de Agua</h2>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              className={classes.addButton}
              onClick={handleOpenAddDialog}
            >
              Agregar
            </Button>
          </Grid>
        </Grid>
      </div>
      <Divider />

      <div style={{ marginTop: '20px', marginBottom: '20px' }}>
        {showTable && (
          <WaterTankCollapsibleTable
            tanks={waterTanks}
            onExecute={handleExecuteTank}
            onEdit={handleEditTank}
            onDelete={handleDeleteTank}
            handleAlert={handleAlert}
            waitingForConfirmation={waitingForConfirmation}
            currentMac={currentMac}
            canIdIrrigation={canIdIrrigation}
          />
        )}
      </div>

      {/* Diálogo para agregar o editar un tanque */}
      <AddWaterTankDialog
        open={openAddDialog}
        onClose={handleCloseAddDialog}
        onSuccess={handleAddSuccess}
        editingTank={editingTank}
      />

      {/* Diálogo de confirmación para eliminar tanque */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Confirmar eliminación
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            ¿Está seguro de que desea eliminar el tanque "{tankToDelete?.name}"?
            <br />
            Esta acción no se puede deshacer y se eliminará toda la configuración asociada al tanque.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="primary">
            Cancelar
          </Button>
          <Button onClick={handleConfirmDeleteTank} color="secondary" autoFocus>
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>

      <div>
        <ConfirmationAlerts
          open={open}
          setOpen={setOpen}
          typeAlert={typeAlert}
          message={alertMessage}
          alertTitle={alertTitle}
        />
      </div>
      <div>
        <ConfirmationAlerts
          open={openNotification}
          setOpen={setOpenNotification}
          typeAlert={typeNotification}
          message={notificationMessage}
          alertTitle={notificationTitle}
        />
      </div>

      {/* Alert para validación MQTT */}
      {mqttValidationAlert.show && (
        // <div style={{ position: 'fixed', top: '20px', right: '20px', zIndex: 9999 }}>
        <div>
          <Alert
            severity={mqttValidationAlert.type}
            onClose={() => setMqttValidationAlert({ show: false, type: '', message: '' })}
          >
            {mqttValidationAlert.message}
          </Alert>
        </div>
      )}
    </>
  );
};

export default withRouter(WaterTankForm);
export { WaterTankForm };
