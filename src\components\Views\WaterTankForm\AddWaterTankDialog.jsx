import React, { useState, useContext, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Grid,
  makeStyles,
  Divider,
  Tabs,
  Tab,
  Box,
} from '@material-ui/core';
import { UserContext } from '../../../context/UserProvider';
import { db } from '../../../config/firebase';

const useStyles = makeStyles((theme) => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 200,
  },
  sectionTitle: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(1),
  },
  divider: {
    margin: theme.spacing(2, 0),
  },
  tabPanel: {
    paddingTop: theme.spacing(2),
  },
}));

// Componente TabPanel para las pestañas
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box p={3}>
          {children}
        </Box>
      )}
    </div>
  );
}

const AddWaterTankDialog = ({ open, onClose, onSuccess, editingTank }) => {
  const classes = useStyles();
  const { usuario, currentMac, canIdIrrigation, togglesNames, togglesUid, switchesNames,
    switchesUid,levelSensorsUid, levelSensorsNames } = useContext(UserContext);

  // Estados para los campos del formulario
  const [tankName, setTankName] = useState('');
  const [fillPump, setFillPump] = useState('');
  const [fillingWaterSource, setFillingWaterSource] = useState('');
  const [fillValve, setFillValve] = useState(''); // Válvula de llenado (única)
  const [emptyPump, setEmptyPump] = useState('');
  const [emptyingTank, setEmptyingTank] = useState('');
  const [emptyValve, setEmptyValve] = useState(''); // Válvula de vaciado (única)
  const [minLevelSensor, setMinLevelSensor] = useState('');
  const [maxLevelSensor, setMaxLevelSensor] = useState('');
  const [waterLevelSensor, setWaterLevelSensor] = useState(''); // Sensor global de nivel de agua
  const [recirculationValve, setRecirculationValve] = useState(''); // Electroválvula de recirculación
  const [recirculationPump, setRecirculationPump] = useState(''); // Bomba de recirculación
  const [emptyTankLevelSensor, setEmptyTankLevelSensor] = useState(''); // Sensor de nivel para el tanque de vaciado/trasvase
  const [waterSourceLevelSensor, setWaterSourceLevelSensor] = useState(''); // Sensor de nivel para la fuente de agua
  // Estados para los tiempos
  const [fillTimeMinutes, setFillTimeMinutes] = useState('');
  const [fillTimeSeconds, setFillTimeSeconds] = useState('');
  const [emptyTimeMinutes, setEmptyTimeMinutes] = useState('');
  const [emptyTimeSeconds, setEmptyTimeSeconds] = useState('');
  const [waitTimeMinutes, setWaitTimeMinutes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [tabValue, setTabValue] = useState(0); // Estado para las pestañas

  // Estado para rastrear todas las selecciones únicas (usado internamente para validación)
  const [selectedUniqueOptions, setSelectedUniqueOptions] = useState({
    pumps: [], // Para bombas de llenado y vaciado
    tanks: [], // Para fuente de agua y tanque de vaciado
    specialValves: [], // Para válvulas especiales como la de recirculación
  });

  // Efecto para cargar los datos del tanque cuando se está editando
  useEffect(() => {
    if (editingTank) {
      console.log("Esto es editingTank",editingTank);
      // Buscar los nombres correspondientes a los UIDs
      const fillData = editingTank.fillData;
      const emptyData = editingTank.emptyData;

      // Establecer el nombre del tanque
      setTankName(editingTank.name);

      // Establecer el sensor de nivel de agua
      setWaterLevelSensor(editingTank.waterLevelSensor || '');

      // Establecer la electroválvula de recirculación
      const recirculationValveIndex = togglesUid.findIndex(uid => uid === editingTank.recirculationValve);
      if (recirculationValveIndex !== -1) {
        setRecirculationValve(togglesNames[recirculationValveIndex]);
      } else {
        setRecirculationValve('');
      }

      // Establecer la bomba de recirculación
      const recirculationPumpIndex = togglesUid.findIndex(uid => uid === editingTank.recirculationPump);
      if (recirculationPumpIndex !== -1) {
        setRecirculationPump(togglesNames[recirculationPumpIndex]);
      } else {
        setRecirculationPump('');
      }

      // Configuración de llenado
      if (fillData) {
        // Buscar el nombre de la bomba de llenado
        const fillPumpIndex = togglesUid.findIndex(uid => uid === fillData.pump);
        if (fillPumpIndex !== -1) {
          setFillPump(togglesNames[fillPumpIndex]);
        }

        // Buscar el nombre de la fuente de agua
        if (fillData.waterSource) {
          const waterSourceIndex = togglesUid.findIndex(uid => uid === fillData.waterSource);
          if (waterSourceIndex !== -1) {
            setFillingWaterSource(togglesNames[waterSourceIndex]);
          }
        }

        // Buscar el nombre de la válvula de llenado
        if (fillData.fillValve) {
          const fillValveIndex = togglesUid.findIndex(uid => uid === fillData.fillValve);
          if (fillValveIndex !== -1) {
            setFillValve(togglesNames[fillValveIndex]);
          }
        }

        // Establecer el sensor de nivel máximo
        setMaxLevelSensor(fillData.maxSwitch || '');

        // Establecer los tiempos de llenado
        setFillTimeMinutes(fillData.minutes || '');
        setFillTimeSeconds(fillData.seconds || '');

        // Establecer el sensor de nivel de la fuente de agua
        setWaterSourceLevelSensor(fillData.waterSourceLevelSensor || '');
      }

      // Configuración de vaciado
      if (emptyData) {
        // Buscar el nombre de la bomba de vaciado
        const emptyPumpIndex = togglesUid.findIndex(uid => uid === emptyData.pump);
        if (emptyPumpIndex !== -1) {
          setEmptyPump(togglesNames[emptyPumpIndex]);
        }

        // Buscar el nombre del tanque receptor
        if (emptyData.targetTank) {
          const targetTankIndex = togglesUid.findIndex(uid => uid === emptyData.targetTank);
          if (targetTankIndex !== -1) {
            setEmptyingTank(togglesNames[targetTankIndex]);
          }
        }

        // Buscar el nombre de la válvula de vaciado
        if (emptyData.emptyValve) {
          const emptyValveIndex = togglesUid.findIndex(uid => uid === emptyData.emptyValve);
          if (emptyValveIndex !== -1) {
            setEmptyValve(togglesNames[emptyValveIndex]);
          }
        }

        // Establecer el sensor de nivel mínimo
        setMinLevelSensor(emptyData.minSwitch || '');

        // Establecer los tiempos de vaciado
        setEmptyTimeMinutes(emptyData.minutes || '');
        setEmptyTimeSeconds(emptyData.seconds || '');
        setWaitTimeMinutes(emptyData.minutesWaiting || '');

        // Establecer el sensor de nivel del tanque de vaciado
        setEmptyTankLevelSensor(emptyData.targetTankWaterLevelS || '');
      }
    }
  }, [editingTank, togglesNames, togglesUid]);

  // Efecto para actualizar las selecciones únicas cuando cambian los valores
  useEffect(() => {
    const pumps = [];
    const tanks = [];
    const specialValves = []; // Para válvulas especiales como la de recirculación

    // Agregar bombas seleccionadas (las bombas pueden repetirse entre sí)
    if (fillPump) pumps.push(fillPump);
    if (emptyPump) pumps.push(emptyPump);
    if (recirculationPump) pumps.push(recirculationPump);

    // Agregar tanques seleccionados
    if (fillingWaterSource) tanks.push(fillingWaterSource);
    if (emptyingTank && emptyingTank !== fillingWaterSource) tanks.push(emptyingTank);

    // Agregar válvula de recirculación
    if (recirculationValve) specialValves.push(recirculationValve);

    setSelectedUniqueOptions({ pumps, tanks, specialValves });

    // Limpiar las válvulas que ahora son bombas, tanques o válvulas especiales
    if (fillValve && (fillPump === fillValve || emptyPump === fillValve || recirculationPump === fillValve ||
        fillingWaterSource === fillValve || emptyingTank === fillValve || recirculationValve === fillValve)) {
      setFillValve('');
    }

    if (emptyValve && (fillPump === emptyValve || emptyPump === emptyValve || recirculationPump === emptyValve ||
        fillingWaterSource === emptyValve || emptyingTank === emptyValve || recirculationValve === emptyValve)) {
      setEmptyValve('');
    }
  }, [fillPump, emptyPump, recirculationPump, fillingWaterSource, emptyingTank, fillValve, emptyValve, recirculationValve]);

  const handleSubmit = async () => {
    // Validación básica
    if (!tankName.trim()) {
      setError('El nombre del tanque es obligatorio');
      return;
    }

    if (!fillPump || !emptyPump) {
      setError('Debe seleccionar bombas para llenado y vaciado');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Obtener el índice de las bombas seleccionadas para obtener sus UIDs
      const fillPumpIndex = togglesNames.findIndex(name => name === fillPump);
      const emptyPumpIndex = togglesNames.findIndex(name => name === emptyPump);

      if (fillPumpIndex === -1 || emptyPumpIndex === -1) {
        throw new Error('No se encontraron las bombas seleccionadas');
      }

      const fillPumpUid = togglesUid[fillPumpIndex];
      const emptyPumpUid = togglesUid[emptyPumpIndex];

      // Obtener UID de la válvula de llenado
      let fillValveUid = null;
      if (fillValve) {
        const fillValveIndex = togglesNames.findIndex(name => name === fillValve);
        if (fillValveIndex !== -1) {
          fillValveUid = togglesUid[fillValveIndex];
        }
      }

      // Obtener UID de la válvula de vaciado
      let emptyValveUid = null;
      if (emptyValve) {
        const emptyValveIndex = togglesNames.findIndex(name => name === emptyValve);
        if (emptyValveIndex !== -1) {
          emptyValveUid = togglesUid[emptyValveIndex];
        }
      }

      // Obtener UID de la fuente de agua si está seleccionada
      let waterSourceUid = null;
      if (fillingWaterSource) {
        const waterSourceIndex = togglesNames.findIndex(name => name === fillingWaterSource);
        if (waterSourceIndex !== -1) {
          waterSourceUid = togglesUid[waterSourceIndex];
        }
      }

      // Obtener UID del tanque receptor si está seleccionado
      let emptyingTankUid = null;
      if (emptyingTank) {
        const emptyingTankIndex = togglesNames.findIndex(name => name === emptyingTank);
        if (emptyingTankIndex !== -1) {
          emptyingTankUid = togglesUid[emptyingTankIndex];
        }
      }

      // Obtener UID de la electroválvula de recirculación si está seleccionada
      let recirculationValveUid = null;
      if (recirculationValve) {
        const recirculationValveIndex = togglesNames.findIndex(name => name === recirculationValve);
        if (recirculationValveIndex !== -1) {
          recirculationValveUid = togglesUid[recirculationValveIndex];
        }
      }

      // Obtener UID de la bomba de recirculación si está seleccionada
      let recirculationPumpUid = null;
      if (recirculationPump) {
        const recirculationPumpIndex = togglesNames.findIndex(name => name === recirculationPump);
        if (recirculationPumpIndex !== -1) {
          recirculationPumpUid = togglesUid[recirculationPumpIndex];
        }
      }

      // Crear el objeto del tanque (nuevo o actualizado)
      const tankData = {
        name: tankName,
        action: editingTank ? editingTank.action || "fill" : "fill", // Mantener la acción actual o usar "fill" por defecto
        lastExecution: editingTank ? editingTank.lastExecution : "Never", // Mantener la última ejecución o usar "Never"
        levelSensor: waterLevelSensor || null, // Sensor global de nivel de agua
        recirculationValve: recirculationValveUid, // Electroválvula de recirculación
        recirculationPump: recirculationPumpUid, // Bomba de recirculación
        fill: {
          pump: fillPumpUid,
          maxSwitch: maxLevelSensor || null, // Corregido: maxSwitch debe usar maxLevelSensor
          waterSource: waterSourceUid,
          waterSourceLevelSensor: waterSourceLevelSensor || null, // Sensor de nivel de la fuente de agua
          fillValve: fillValveUid, // Válvula de llenado única
          minutes: fillTimeMinutes,
          seconds: fillTimeSeconds,
        },
        empty: {
          pump: emptyPumpUid,
          minSwitch: minLevelSensor || null, // Corregido: minSwitch debe usar minLevelSensor
          targetTank: emptyingTankUid,
          emptyValve: emptyValveUid, // Válvula de vaciado única
          minutes: emptyTimeMinutes,
          seconds: emptyTimeSeconds,
          minutesWaiting: waitTimeMinutes,
          targetTankWaterLevelS: emptyTankLevelSensor || null,
        }
      };

      // Ruta donde se guardarán los datos
      const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
      const waterDocRef = db.collection(waterTankAddr).doc("tankRows");

      // Obtener los tanques actuales
      const snapshot = await waterDocRef.get();
      let allRows = [];

      if (snapshot.exists) {
        const data = snapshot.data();
        allRows = [...data.allRows];
      }

      if (editingTank) {
        // Actualizar el tanque existente
        allRows = allRows.map((row, index) => {
          if (index === editingTank.id) {
            // Mantener la acción actual y la última ejecución
            return {
              ...tankData,
              action: editingTank.action || "fill",
              lastExecution: editingTank.lastExecution || "Never"
            };
          }
          return row;
        });
      } else {
        // Agregar un nuevo tanque
        allRows.push(tankData);
      }

      // Guardar en Firebase
      await waterDocRef.set({ allRows });

      // Limpiar el formulario y cerrar el diálogo
      resetForm();
      onSuccess(allRows, !!editingTank); // Pasar true si estamos editando
    } catch (error) {
      console.error("Error al guardar el tanque:", error);
      setError(`Error al guardar: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setTankName('');
    setFillPump('');
    setFillingWaterSource('');
    setFillValve(''); // Resetear la válvula de llenado
    setEmptyPump('');
    setEmptyingTank('');
    setEmptyValve(''); // Resetear la válvula de vaciado
    setMinLevelSensor('');
    setMaxLevelSensor('');
    setWaterLevelSensor(''); // Resetear el sensor global de nivel de agua
    setRecirculationValve(''); // Resetear la electroválvula de recirculación
    setRecirculationPump(''); // Resetear la bomba de recirculación
    setEmptyTankLevelSensor(''); // Resetear el sensor de nivel del tanque de vaciado
    setWaterSourceLevelSensor(''); // Resetear el sensor de nivel de la fuente de agua
    // Resetear los campos de tiempo
    setFillTimeMinutes('');
    setFillTimeSeconds('');
    setEmptyTimeMinutes('');
    setEmptyTimeSeconds('');
    setWaitTimeMinutes('');
    setError('');
    setSelectedUniqueOptions({ pumps: [], tanks: [], specialValves: [] });
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Función para filtrar opciones disponibles para bombas (pueden repetirse entre sí)
  const getAvailablePumpOptions = (currentSelection) => {
    return togglesNames.filter(name => {
      // Las bombas pueden repetirse entre sí, así que no hay restricciones entre bombas
      // Solo verificamos que no esté seleccionada como tanque o válvula de recirculación

      // Si ya está seleccionada como tanque, no mostrarla
      if (fillingWaterSource === name || emptyingTank === name) {
        return false;
      }

      // Si ya está seleccionada como válvula de recirculación, no mostrarla
      if (recirculationValve === name) {
        return false;
      }

      return true;
    });
  };

  // Función para filtrar opciones disponibles para tanques
  const getAvailableTankOptions = (currentSelection) => {
    return togglesNames.filter(name => {
      // Si es la selección actual, siempre mostrarla
      if (name === currentSelection) return true;

      // Si ya está seleccionada como tanque, no mostrarla
      if ((fillingWaterSource === name && currentSelection !== fillingWaterSource) ||
          (emptyingTank === name && currentSelection !== emptyingTank)) {
        return false;
      }

      // Si ya está seleccionada como bomba (incluyendo bomba de recirculación), no mostrarla
      if (fillPump === name || emptyPump === name || recirculationPump === name) {
        return false;
      }

      // Si ya está seleccionada como válvula de recirculación, no mostrarla
      if (recirculationValve === name) {
        return false;
      }

      return true;
    });
  };

  // Función para filtrar opciones disponibles para válvulas
  const getAvailableValveOptions = (currentSelection = '') => {
    return togglesNames.filter(name => {
      // Si es la selección actual, siempre mostrarla
      if (name === currentSelection) return true;

      // Si ya está seleccionada como bomba (incluyendo bomba de recirculación) o tanque, no mostrarla
      if (fillPump === name || emptyPump === name || recirculationPump === name ||
          fillingWaterSource === name || emptyingTank === name) {
        return false;
      }

      // Si ya está seleccionada como válvula de recirculación, no mostrarla
      if (recirculationValve === name) {
        return false;
      }

      // Si ya está seleccionada como otra válvula (llenado o vaciado), no mostrarla
      if ((fillValve === name && currentSelection !== fillValve) ||
          (emptyValve === name && currentSelection !== emptyValve)) {
        return false;
      }

      return true;
    });
  };

  // Función para filtrar opciones disponibles para la válvula de recirculación
  const getAvailableRecirculationValveOptions = (currentSelection) => {
    return togglesNames.filter(name => {
      // Si es la selección actual, siempre mostrarla
      if (name === currentSelection) return true;

      // Si ya está seleccionada como bomba (incluyendo bomba de recirculación), no mostrarla
      if (fillPump === name || emptyPump === name || recirculationPump === name) {
        return false;
      }

      // Si ya está seleccionada como tanque, no mostrarla
      if (fillingWaterSource === name || emptyingTank === name) {
        return false;
      }

      return true;
    });
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>{editingTank ? 'Editar Tanque de Agua' : 'Agregar Nuevo Tanque de Agua'}</DialogTitle>
      <DialogContent>
        {error && (
          <Typography color="error" variant="body2" gutterBottom>
            {error}
          </Typography>
        )}

        <TextField
          autoFocus
          margin="dense"
          id="name"
          label="Nombre del Tanque"
          type="text"
          fullWidth
          value={tankName}
          onChange={(e) => setTankName(e.target.value)}
          variant="outlined"
        />

        <Typography variant="h6" className={classes.sectionTitle}>
          Sensor de Monitoreo de Nivel
        </Typography>

        <Typography variant="body2" color="textSecondary" gutterBottom>
          Seleccione el sensor que monitorea el nivel de agua en el tanque. Este sensor se utilizará para visualizar el nivel actual del agua.
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="water-level-sensor-label">Sensor de Nivel de Agua</InputLabel>
              <Select
                labelId="water-level-sensor-label"
                id="water-level-sensor"
                value={waterLevelSensor}
                onChange={(e) => setWaterLevelSensor(e.target.value)}
                label="Sensor de Nivel de Agua (Monitoreo)"
              >
                <MenuItem value="">
                  <em>Ninguno</em>
                </MenuItem>
                {levelSensorsNames.map((name, index) => (
                  <MenuItem key={`water-level-${index}`} value={levelSensorsUid[index]}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Divider className={classes.divider} />

        <Typography variant="h6" className={classes.sectionTitle}>
          Configuración de Recirculación
        </Typography>

        <Typography variant="body2" color="textSecondary" gutterBottom>
          Seleccione la bomba y electroválvula de recirculación para el tanque (opcional). Estos componentes son generales para el tanque y no corresponden a operaciones específicas de llenado o vaciado.
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="recirculation-pump-label">Bomba de Recirculación</InputLabel>
              <Select
                labelId="recirculation-pump-label"
                id="recirculation-pump"
                value={recirculationPump}
                onChange={(e) => setRecirculationPump(e.target.value)}
                label="Bomba de Recirculación"
              >
                <MenuItem value="">
                  <em>No Aplica</em>
                </MenuItem>
                {getAvailablePumpOptions(recirculationPump).map((name, index) => (
                  <MenuItem key={`recirculation-pump-${index}`} value={name}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="recirculation-valve-label">Electroválvula de Recirculación</InputLabel>
              <Select
                labelId="recirculation-valve-label"
                id="recirculation-valve"
                value={recirculationValve}
                onChange={(e) => setRecirculationValve(e.target.value)}
                label="Electroválvula de Recirculación"
              >
                <MenuItem value="">
                  <em>No Aplica</em>
                </MenuItem>
                {getAvailableRecirculationValveOptions(recirculationValve).map((name, index) => (
                  <MenuItem key={`recirculation-valve-${index}`} value={name}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Divider className={classes.divider} />

        {/* Pestañas para Llenado y Vaciado */}
        <Tabs value={tabValue} onChange={handleTabChange} indicatorColor="primary" aria-label="Configuración de tanque">
          <Tab label="Llenado" />
          <Tab label="Vaciado" />
        </Tabs>

        {/* Pestaña de Llenado */}
        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" className={classes.sectionTitle}>
            Configuración de Llenado
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" className={classes.formControl} fullWidth>
                <InputLabel id="fill-pump-label">Bomba de Llenado</InputLabel>
                <Select
                  labelId="fill-pump-label"
                  id="fill-pump"
                  value={fillPump}
                  onChange={(e) => setFillPump(e.target.value)}
                  label="Bomba de Llenado"
                >
                  {getAvailablePumpOptions(fillPump).map((name, index) => (
                    <MenuItem key={`fill-${index}`} value={name}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" className={classes.formControl} fullWidth>
                <InputLabel id="fill-waterSource-label">Fuente de agua</InputLabel>
                <Select
                  labelId="fill-waterSource-label"
                  id="fill-waterSource"
                  value={fillingWaterSource}
                  onChange={(e) => setFillingWaterSource(e.target.value)}
                  label="Fuente de agua"
                >
                  {getAvailableTankOptions(fillingWaterSource).map((name, index) => (
                    <MenuItem key={`fill-waterS-${index}`} value={name}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" className={classes.formControl} fullWidth>
                <InputLabel id="water-source-level-sensor-label">Sensor de Nivel de Fuente de Agua</InputLabel>
                <Select
                  labelId="water-source-level-sensor-label"
                  id="water-source-level-sensor"
                  value={waterSourceLevelSensor}
                  onChange={(e) => setWaterSourceLevelSensor(e.target.value)}
                  label="Sensor de Nivel de Fuente de Agua"
                >
                  <MenuItem value="">
                    <em>Ninguno</em>
                  </MenuItem>
                  {levelSensorsNames.map((name, index) => (
                    <MenuItem key={`water-source-level-${index}`} value={levelSensorsUid[index]}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" className={classes.formControl} fullWidth>
                <InputLabel id="fill-valve-label">Válvula de llenado</InputLabel>
                <Select
                  labelId="fill-valve-label"
                  id="fill-valve"
                  value={fillValve}
                  onChange={(e) => setFillValve(e.target.value)}
                  label="Válvula de llenado"
                >
                  <MenuItem value="">
                    <em>Ninguna</em>
                  </MenuItem>
                  {getAvailableValveOptions(fillValve).map((name, index) => (
                    <MenuItem key={`fill-valve-${index}`} value={name}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" className={classes.formControl} fullWidth>
                <InputLabel id="max-sensor-label">Sensor de Nivel Máximo</InputLabel>
                <Select
                  labelId="max-sensor-label"
                  id="max-sensor"
                  value={maxLevelSensor}
                  onChange={(e) => setMaxLevelSensor(e.target.value)}
                  label="Sensor de Nivel Máximo"
                >
                  {switchesNames.map((name, index) => (
                    <MenuItem key={`max-${index}`} value={switchesUid[index]}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Tiempo Total de Llenado
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                margin="dense"
                id="fill-time-minutes"
                label="Minutos"
                type="number"
                fullWidth
                value={fillTimeMinutes}
                onChange={(e) => { if(e.target.value <= 60 && e.target.value >= 0) setFillTimeMinutes(e.target.value) }}
                variant="outlined"
                InputProps={{ inputProps: { min: 0, max: 60 } }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                margin="dense"
                id="fill-time-seconds"
                label="Segundos"
                type="number"
                fullWidth
                value={fillTimeSeconds}
                onChange={(e) => { if(e.target.value <= 59 && e.target.value >= 0) setFillTimeSeconds(e.target.value) }}
                variant="outlined"
                InputProps={{ inputProps: { min: 0, max: 59 } }}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Pestaña de Vaciado */}
        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" className={classes.sectionTitle}>
            Configuración de Vaciado
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" className={classes.formControl} fullWidth>
                <InputLabel id="empty-pump-label">Bomba de Vaciado</InputLabel>
                <Select
                  labelId="empty-pump-label"
                  id="empty-pump"
                  value={emptyPump}
                  onChange={(e) => setEmptyPump(e.target.value)}
                  label="Bomba de Vaciado"
                >
                  {getAvailablePumpOptions(emptyPump).map((name, index) => (
                    <MenuItem key={`empty-${index}`} value={name}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" className={classes.formControl} fullWidth>
                <InputLabel id="empty-waterSource-label">Tanque receptor/de trasvase(opcional)</InputLabel>
                <Select
                  labelId="empty-waterSource-label"
                  id="empty-waterSource"
                  value={emptyingTank}
                  onChange={(e) => setEmptyingTank(e.target.value)}
                  label="Tanque receptor/de trasvase(opcional)"
                >
                  <MenuItem value="">
                    <em>No Aplica</em>
                  </MenuItem>
                  {getAvailableTankOptions(emptyingTank).map((name, index) => (
                    <MenuItem key={`empty-waterS-${index}`} value={name}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" className={classes.formControl} fullWidth>
                <InputLabel id="empty-valve-label">Válvula de vaciado</InputLabel>
                <Select
                  labelId="empty-valve-label"
                  id="empty-valve"
                  value={emptyValve}
                  onChange={(e) => setEmptyValve(e.target.value)}
                  label="Válvula de vaciado"
                >
                  <MenuItem value="">
                    <em>Ninguna</em>
                  </MenuItem>
                  {getAvailableValveOptions(emptyValve).map((name, index) => (
                    <MenuItem key={`empty-valve-${index}`} value={name}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" className={classes.formControl} fullWidth>
                <InputLabel id="min-sensor-label">Sensor de Nivel Mínimo</InputLabel>
                <Select
                  labelId="min-sensor-label"
                  id="min-sensor"
                  value={minLevelSensor}
                  onChange={(e) => setMinLevelSensor(e.target.value)}
                  label="Sensor de Nivel Mínimo"
                >
                  {switchesNames.map((name, index) => (
                    <MenuItem key={`min-${index}`} value={switchesUid[index]}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Sensor de Nivel para Tanque de Vaciado/Trasvase
              </Typography>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Seleccione un sensor de nivel para monitorear el tanque de vaciado/trasvase (opcional).
                Este sensor se utilizará para visualizar el nivel de agua en el tanque receptor.
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" className={classes.formControl} fullWidth>
                <InputLabel id="empty-tank-level-sensor-label">Sensor de Nivel para Tanque de Vaciado</InputLabel>
                <Select
                  labelId="empty-tank-level-sensor-label"
                  id="empty-tank-level-sensor"
                  value={emptyTankLevelSensor}
                  onChange={(e) => setEmptyTankLevelSensor(e.target.value)}
                  label="Sensor de Nivel para Tanque de Vaciado"
                >
                  <MenuItem value="">
                    <em>No Aplica</em>
                  </MenuItem>
                  {levelSensorsNames.map((name, index) => (
                    <MenuItem key={`empty-tank-level-${index}`} value={levelSensorsUid[index]}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Tiempo Total de Vaciado
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                margin="dense"
                id="empty-time-minutes"
                label="Minutos"
                type="number"
                fullWidth
                value={emptyTimeMinutes}
                onChange={(e) => { if(e.target.value <= 60 && e.target.value >= 0) setEmptyTimeMinutes(e.target.value) }}
                variant="outlined"
                InputProps={{ inputProps: { min: 0, max: 60 } }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                margin="dense"
                id="empty-time-seconds"
                label="Segundos"
                type="number"
                fullWidth
                value={emptyTimeSeconds}
                onChange={(e) => { if(e.target.value <= 59 && e.target.value >= 0) setEmptyTimeSeconds(e.target.value) }}
                variant="outlined"
                InputProps={{ inputProps: { min: 0, max: 59 } }}
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Tiempo de Espera para Vaciado
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                margin="dense"
                id="wait-time-minutes"
                label="Minutos"
                type="number"
                fullWidth
                value={waitTimeMinutes}
                onChange={(e) => { if(e.target.value <= 60 && e.target.value >= 0) setWaitTimeMinutes(e.target.value)}}
                variant="outlined"
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
          </Grid>
        </TabPanel>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="primary" disabled={isSubmitting}>
          Cancelar
        </Button>
        <Button
          onClick={handleSubmit}
          color="primary"
          variant="contained"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Guardando...' : (editingTank ? 'Actualizar' : 'Guardar')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddWaterTankDialog;
