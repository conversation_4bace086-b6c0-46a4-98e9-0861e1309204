import { useEffect, useContext, useRef, useState, forwardRef, useImperativeHandle, useCallback } from 'react';
import { db } from '../../../config/firebase';
import { UserContext } from '../../../context/UserProvider';

/**
 * WaterTankMonitor - Componente que monitorea las operaciones de tanques de agua en segundo plano
 * Este componente funciona independientemente de si el usuario está en la sección WaterTankForm
 * y actualiza automáticamente los estados en Firebase cuando las operaciones terminan.
 */
const WaterTankMonitor = forwardRef(({ onNotification }, ref) => {
  const { usuario, currentMac, canIdIrrigation, togglesUid, togglesNames, switchesUid } = useContext(UserContext);
  const [monitoredTanks, setMonitoredTanks] = useState([]);
  const unsubscribeRefs = useRef({});
  const tankTimeoutsRef = useRef({});

  // Función para obtener el estado de un switch
  const getSwitchState = async (switchUid, usuario) => {
    try {
      const arrayDeCadenas = switchUid.split("@");
      const mac = arrayDeCadenas[0];
      const canId = arrayDeCadenas[1];
      const outId = arrayDeCadenas[3];
      
      const docPath = `${usuario.username}/infoDevices/${mac}/${canId}/fromModule/switchesInfo`;
      const docRef = db.doc(docPath);
      const snapshot = await docRef.get();
      
      if (snapshot.exists) {
        const data = snapshot.data();
        if (data && data.states && data.states[Number(outId)] !== undefined) {
          return data.states[Number(outId)];
        }
      }
      return null;
    } catch (error) {
      console.error("Error al obtener estado del switch:", error);
      return null;
    }
  };

  // Función para actualizar el currentStatus en Firebase
  const updateCurrentStatusInDB = useCallback(async (tankId, isRunning, typeOfAction) => {
    try {
      console.log(`[WaterTankMonitor] Actualizando currentStatus para tanque ${tankId}:`, { isRunning, typeOfAction });
      const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
      const waterDocRef = db.collection(waterTankAddr).doc("tankRows");

      const snapshot = await waterDocRef.get();
      if (snapshot.exists) {
        const data = snapshot.data();
        let allRows = [...data.allRows];
        console.log("Esto es allROws:",allRows)
        console.log("Esto es typeOfAction:",typeOfAction)

        allRows = allRows.map((row, index) => {
          if (index === tankId) {
            return {
              ...row,
              currentStatus: {
                isRunning: isRunning,
                typeOfAction: typeOfAction
              }
            };
          }
          return row;
        });
        console.log("Esto es despues allRows de currentStatus:",allRows)

        await waterDocRef.set({ allRows });
        console.log(`[WaterTankMonitor] CurrentStatus actualizado en DB para tanque ${tankId}`);
      }
    } catch (error) {
      console.error("[WaterTankMonitor] Error al actualizar currentStatus:", error);
    }
  }, [usuario, currentMac, canIdIrrigation]);

  // Función para actualizar la última ejecución en Firebase
  const updateTankLastExecutionInDB = useCallback(async (tankId, lastExecutionTime, tankAction) => {
    try {
      console.log(`[WaterTankMonitor] Actualizando lastExecution para tanque ${tankId}:`, lastExecutionTime);
      const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
      const waterDocRef = db.collection(waterTankAddr).doc("tankRows");

      const snapshot = await waterDocRef.get();
      if (snapshot.exists) {
        const data = snapshot.data();
        let allRows = [...data.allRows];
        console.log("Esto es allRows:",allRows)

        allRows = allRows.map((row, index) => {
          if (index === tankId) {
            return {
              ...row,
              lastExecution: lastExecutionTime,
              lastAction: tankAction
            };
          }
          return row;
        });

        await waterDocRef.set({ allRows });
        console.log(`[WaterTankMonitor] LastExecution actualizado en DB para tanque ${tankId}`);
      }
    } catch (error) {
      console.error("[WaterTankMonitor] Error al actualizar lastExecution:", error);
    }
  }, [usuario, currentMac, canIdIrrigation]);

  // Función para manejar la finalización de una operación
  const handleOperationCompletion = async (tank, completionReason, completionTime = null) => {
    const finalCompletionTime = completionTime || new Date().toISOString();
    const actionText = tank.action === "fill" ? "llenado" : "vaciado";
    
    console.log(`[WaterTankMonitor] Operación de ${actionText} completada para ${tank.name}. Razón: ${completionReason}`);

    // Actualizar estados en Firebase
    await updateCurrentStatusInDB(tank.id, false, null);
    await updateTankLastExecutionInDB(tank.id, finalCompletionTime, tank.action);

    // Limpiar timeout si existe
    if (tankTimeoutsRef.current[tank.id]) {
      clearTimeout(tankTimeoutsRef.current[tank.id]);
      delete tankTimeoutsRef.current[tank.id];
    }

    // Remover tanque de la lista de monitoreados
    setMonitoredTanks(prev => prev.filter(t => t.id !== tank.id));

    // Emitir evento personalizado para comunicación con otros componentes
    const event = new CustomEvent('waterTankOperationComplete', {
      detail: {
        tankId: tank.id,
        tankName: tank.name,
        action: tank.action,
        completionReason: completionReason,
        completionTime: finalCompletionTime
      }
    });
    window.dispatchEvent(event);

    // Enviar notificación si se proporciona la función
    if (onNotification) {
      let message = "";
      switch (completionReason) {
        case "switch":
          const switchType = tank.action === "fill" ? "máximo" : "mínimo";
          message = `La operación de ${actionText} en ${tank.name} ha sido completada automáticamente porque se activó el sensor de nivel ${switchType}.`;
          break;
        case "timeout":
          message = `La operación de ${actionText} en ${tank.name} ha sido completada por tiempo de ejecución.`;
          break;
        case "firebase":
          message = `La operación de ${actionText} en ${tank.name} ha sido completada`;
          break;
        default:
          message = `La operación de ${actionText} en ${tank.name} ha sido completada.`;
      }

      onNotification({
        type: "success",
        title: "Operación completada",
        message: message,
        tankId: tank.id,
        tankName: tank.name,
        action: tank.action
      });
    }
  };

  // Función para manejar el paro total de todas las operaciones
  const handleEmergencyStop = useCallback(async (completionTime = null) => {
    const finalCompletionTime = completionTime || new Date().toISOString();

    console.log(`[WaterTankMonitor] PARO TOTAL DETECTADO - Deteniendo todas las operaciones activas`);

    // Crear una copia de los tanques monitoreados para evitar problemas de concurrencia
    const tanksToStop = [...monitoredTanks];
    console.log("Esto es tanksToStop:", tanksToStop);
    // Detener cada tanque activo
    for (const tank of tanksToStop) {
      const actionText = tank.action === "fill" ? "llenado" : (tank.action === "empty" ? "vaciado" : "recirculación");

      console.log(`[WaterTankMonitor] Deteniendo operación de ${actionText} para tanque ${tank.name} por paro total`);

      console.log("Esto es tank.action:", tank.action);
      // Actualizar estados en Firebase
      await updateCurrentStatusInDB(tank.id, false, tank.action);
      await updateTankLastExecutionInDB(tank.id, finalCompletionTime, tank.action);

      // Limpiar timeout si existe
      if (tankTimeoutsRef.current[tank.id]) {
        clearTimeout(tankTimeoutsRef.current[tank.id]);
        delete tankTimeoutsRef.current[tank.id];
      }

      // Limpiar listeners específicos del tanque
      if (unsubscribeRefs.current[`switch_${tank.id}`]) {
        unsubscribeRefs.current[`switch_${tank.id}`]();
        delete unsubscribeRefs.current[`switch_${tank.id}`];
      }

      if (unsubscribeRefs.current[`firebase_${tank.id}`]) {
        unsubscribeRefs.current[`firebase_${tank.id}`]();
        delete unsubscribeRefs.current[`firebase_${tank.id}`];
      }
    }

    // Limpiar la lista de tanques monitoreados
    setMonitoredTanks([]);

    // Emitir evento personalizado para comunicación con otros componentes
    const event = new CustomEvent('waterTankEmergencyStop', {
      detail: {
        stoppedTanks: tanksToStop.map(t => ({ id: t.id, name: t.name, action: t.action })),
        completionTime: finalCompletionTime,
        reason: "Paro total de emergencia"
      }
    });
    window.dispatchEvent(event);

    // Enviar notificación global si se proporciona la función
    if (onNotification) {
      const stoppedCount = tanksToStop.length;
      const message = stoppedCount > 0
        ? `Se han detenido ${stoppedCount} operación${stoppedCount > 1 ? 'es' : ''} de tanque${stoppedCount > 1 ? 's' : ''} debido a un paro total de emergencia.`
        : "Se ha ejecutado un paro total de emergencia en el sistema de tanques.";

      onNotification({
        type: "warning",
        title: "Paro Total de Emergencia",
        message: message,
        isEmergencyStop: true,
        stoppedTanks: tanksToStop
      });
    }

    // Limpiar el documento de Firebase después de 5 segundos para evitar ciclos infinitos
    // Esto es crucial para evitar que el listener siga detectando el paro de emergencia
    setTimeout(async () => {
      try {
        console.log(`[WaterTankMonitor] Limpiando documento de paro de emergencia después de 5 segundos`);
        const docPath = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/fromModule/configOK`;
        const docRef = db.doc(docPath);

        await docRef.update({
          accion: 0,
          act: "-"
        });

        console.log(`[WaterTankMonitor] Documento de paro de emergencia limpiado exitosamente`);
      } catch (error) {
        console.error(`[WaterTankMonitor] Error al limpiar documento de paro de emergencia:`, error);
      }
    }, 6000);

  }, [monitoredTanks, onNotification, updateCurrentStatusInDB, updateTankLastExecutionInDB, usuario, currentMac, canIdIrrigation]);

  const handleTimeoutCompletion = (tank) => {
    if (onNotification) {
      let message = `Finalizo el tiempo de espera y la operación de vaciado en ${tank.name} ha iniciado.`;

      onNotification({
        type: "info",
        title: "Incio el vaciado",
        message: message,
        tankId: tank.id,
        tankName: tank.name,
        action: tank.action
      });
    }
  };

  // Función para configurar el timeout de una operación
  const setupOperationTimeout = (tank) => {
    const totalTimeMs = (tank.totalMinutes * 60 + tank.totalSeconds) * 1000;
    
    if (totalTimeMs > 0) {
      tankTimeoutsRef.current[tank.id] = setTimeout(() => {
        console.log(`[WaterTankMonitor] Timeout alcanzado para tanque ${tank.name}`);
        handleOperationCompletion(tank, "timeout");
      }, totalTimeMs);
      
      console.log(`[WaterTankMonitor] Timeout configurado para tanque ${tank.name}: ${totalTimeMs}ms`);
    }
  };

  // Función para monitorear switches de un tanque
  const monitorTankSwitches = (tank) => {
    const switchUid = tank.action === "fill" ? tank.fillData?.maxSwitch : tank.emptyData?.minSwitch;
    
    if (!switchUid) {
      console.log(`[WaterTankMonitor] No hay switch configurado para tanque ${tank.name}`);
      return;
    }

    const arrayDeCadenas = switchUid.split("@");
    const mac = arrayDeCadenas[0];
    const canId = arrayDeCadenas[1];
    
    const docPath = `${usuario.username}/infoDevices/${mac}/${canId}/fromModule/switchesInfo`;
    const docRef = db.doc(docPath);

    const unsubscribe = docRef.onSnapshot((snapshot) => {
      if (snapshot.exists) {
        const data = snapshot.data();
        if (data && data.states) {
          const outId = Number(arrayDeCadenas[3]);
          const switchState = data.states[outId];
          
          // Verificar condición de finalización según el tipo de operación
          let shouldComplete = false;
          if (tank.action === "fill" && switchState === 1) {
            shouldComplete = true; // Llenado completo cuando switch máximo se activa
          } else if (tank.action === "empty" && switchState === 0) {
            shouldComplete = true; // Vaciado completo cuando switch mínimo se desactiva
          }

          if (shouldComplete) {
            console.log(`[WaterTankMonitor] Switch activado para tanque ${tank.name}, completando operación`);
            handleOperationCompletion(tank, "switch");
          }
        }
      }
    });

    unsubscribeRefs.current[`switch_${tank.id}`] = unsubscribe;
  };

  // Función para monitorear confirmaciones MQTT de Firebase
  const monitorFirebaseConfirmations = (tank) => {
    const docPath = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/fromModule/configOK`;
    const docRef = db.doc(docPath);

    const unsubscribe = docRef.onSnapshot((snapshot) => {
      if (snapshot.exists) {
        const data = snapshot.data();

        if (data) {
          // Verificar parámetros específicos para detención de operación
          const actionKind = tank.action === "fill" ? 1 : (tank.action === "empty" ? 0 : 3);

          if (
            data.accion === 244 &&
            data.act === "recOK" &&
            data.kind === actionKind &&
            data.outid === tank.id &&
            data.val === 0
          ) {
            console.log(`[WaterTankMonitor] Confirmación Firebase recibida para tanque ${tank.name}`);
            handleOperationCompletion(tank, "firebase");
          } else if (
            data.accion === 244 &&
            data.act === "recOK" &&
            data.kind === 0 &&
            data.outid === tank.id &&
            data.val === 2
          ) {
            console.log(`[WaterTankMonitor] Termino tiempo de espera de recirculcion para tanque ${tank.name}`);
            handleTimeoutCompletion(tank);
          }
          // Nota: La detección de paro total (kind: 4) se maneja en el listener global
          // para evitar duplicación de lógica en cada tanque individual
        }
      }
    });

    unsubscribeRefs.current[`firebase_${tank.id}`] = unsubscribe;
  };

  // Función para iniciar el monitoreo de un tanque
  const startMonitoring = (tank) => {
    console.log(`[WaterTankMonitor] Iniciando monitoreo para tanque ${tank.name}, acción: ${tank.action}`);
    
    // Solo monitorear operaciones de llenado y vaciado (no recirculación)
    if (tank.action === "fill" || tank.action === "empty" || tank.action === "recirculate") {
      setupOperationTimeout(tank);
      monitorTankSwitches(tank);
      monitorFirebaseConfirmations(tank);
    }
  };

  // Función para detener el monitoreo de un tanque
  const stopMonitoring = (tankId) => {
    console.log(`[WaterTankMonitor] Deteniendo monitoreo para tanque ${tankId}`);
    
    // Limpiar listeners
    if (unsubscribeRefs.current[`switch_${tankId}`]) {
      unsubscribeRefs.current[`switch_${tankId}`]();
      delete unsubscribeRefs.current[`switch_${tankId}`];
    }
    
    if (unsubscribeRefs.current[`firebase_${tankId}`]) {
      unsubscribeRefs.current[`firebase_${tankId}`]();
      delete unsubscribeRefs.current[`firebase_${tankId}`];
    }

    // Limpiar timeout
    if (tankTimeoutsRef.current[tankId]) {
      clearTimeout(tankTimeoutsRef.current[tankId]);
      delete tankTimeoutsRef.current[tankId];
    }
  };

  // Efecto para cargar y monitorear tanques activos
  useEffect(() => {
    if (!usuario || !usuario.username || !currentMac || !canIdIrrigation) {
      return;
    }

    const loadActiveTanks = async () => {
      try {
        const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
        const waterDocRef = db.collection(waterTankAddr).doc("tankRows");
        const snapshot = await waterDocRef.get();

        if (snapshot.exists) {
          const data = snapshot.data();
          const allRows = data.allRows || [];
          
          // Filtrar tanques que están ejecutándose
          const activeTanks = allRows
            .map((row, index) => ({ ...row, id: index }))
            .filter(tank => tank.currentStatus && tank.currentStatus.isRunning);

          console.log(`[WaterTankMonitor] Tanques activos encontrados:`, activeTanks.length);
          
          // Actualizar lista de tanques monitoreados
          setMonitoredTanks(activeTanks);
          
          // Iniciar monitoreo para cada tanque activo
          activeTanks.forEach(tank => {
            // Agregar información adicional necesaria para el monitoreo
            const enhancedTank = {
              ...tank,
              action: tank.currentStatus.typeOfAction,
              totalMinutes: tank.action === "fill" ? tank.fill?.totalMinutes || 0 : tank.empty?.totalMinutes || 0,
              totalSeconds: tank.action === "fill" ? tank.fill?.totalSeconds || 0 : tank.empty?.totalSeconds || 0,
              fillData: tank.fill,
              emptyData: tank.empty
            };
            
            startMonitoring(enhancedTank);
          });
        }
      } catch (error) {
        console.error("[WaterTankMonitor] Error al cargar tanques activos:", error);
      }
    };

    loadActiveTanks();

    // Cleanup al desmontar
    return () => {
      Object.values(unsubscribeRefs.current).forEach(unsubscribe => unsubscribe());
      Object.values(tankTimeoutsRef.current).forEach(timeout => clearTimeout(timeout));
      unsubscribeRefs.current = {};
      tankTimeoutsRef.current = {};
    };
  }, [usuario, currentMac, canIdIrrigation]);

  // Función pública para agregar un tanque al monitoreo (llamada desde WaterTankForm)
  const addTankToMonitoring = (tank) => {
    console.log(`[WaterTankMonitor] Agregando tanque al monitoreo:`, tank.name);

    // Verificar si ya está siendo monitoreado
    const isAlreadyMonitored = monitoredTanks.some(t => t.id === tank.id);
    if (isAlreadyMonitored) {
      console.log(`[WaterTankMonitor] Tanque ${tank.name} ya está siendo monitoreado`);
      return;
    }

    // Agregar a la lista de monitoreados
    setMonitoredTanks(prev => [...prev, tank]);

    // Iniciar monitoreo
    startMonitoring(tank);
  };

  // Función pública para remover un tanque del monitoreo
  const removeTankFromMonitoring = (tankId) => {
    console.log(`[WaterTankMonitor] Removiendo tanque del monitoreo:`, tankId);

    stopMonitoring(tankId);
    setMonitoredTanks(prev => prev.filter(t => t.id !== tankId));
  };

  // Exponer funciones públicas a través de useImperativeHandle si es necesario
  useImperativeHandle(ref, () => ({
    addTankToMonitoring,
    removeTankFromMonitoring,
    getMonitoredTanks: () => monitoredTanks
  }), [monitoredTanks]);

  // Listener para cambios en el documento de tanques (para detectar nuevas operaciones)
  useEffect(() => {
    if (!usuario || !usuario.username || !currentMac || !canIdIrrigation) {
      return;
    }

    const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
    const waterDocRef = db.collection(waterTankAddr).doc("tankRows");

    const unsubscribe = waterDocRef.onSnapshot((snapshot) => {
      if (snapshot.exists) {
        const data = snapshot.data();
        const allRows = data.allRows || [];

        // Buscar tanques que empezaron a ejecutarse y no están siendo monitoreados
        allRows.forEach((row, index) => {
          const tank = { ...row, id: index };

          if (tank.currentStatus && tank.currentStatus.isRunning) {
            const isAlreadyMonitored = monitoredTanks.some(t => t.id === tank.id);

            if (!isAlreadyMonitored && (tank.currentStatus.typeOfAction === "fill" || tank.currentStatus.typeOfAction === "empty" || tank.currentStatus.typeOfAction === "recirculate")) {
              console.log(`[WaterTankMonitor] Nueva operación detectada para tanque ${tank.name || tank.id}`);

              // Agregar información adicional necesaria
              const enhancedTank = {
                ...tank,
                action: tank.currentStatus.typeOfAction,
                totalMinutes: tank.currentStatus.typeOfAction === "fill" ? tank.fill?.totalMinutes || 0 : tank.empty?.totalMinutes || 0,
                totalSeconds: tank.currentStatus.typeOfAction === "fill" ? tank.fill?.totalSeconds || 0 : tank.empty?.totalSeconds || 0,
                fillData: tank.fill,
                emptyData: tank.empty
              };

              addTankToMonitoring(enhancedTank);
            }
          } else {
            // Si el tanque ya no está ejecutándose, removerlo del monitoreo
            const wasMonitored = monitoredTanks.some(t => t.id === tank.id);
            if (wasMonitored) {
              console.log(`[WaterTankMonitor] Tanque ${tank.name || tank.id} ya no está ejecutándose, removiendo del monitoreo`);
              removeTankFromMonitoring(tank.id);
            }
          }
        });
      }
    });

    return () => unsubscribe();
  }, [usuario, currentMac, canIdIrrigation, monitoredTanks]);

  // Listener global para detectar paro total de emergencia
  useEffect(() => {
    if (!usuario || !usuario.username || !currentMac || !canIdIrrigation) {
      return;
    }

    const docPath = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/fromModule/configOK`;
    const docRef = db.doc(docPath);

    const unsubscribe = docRef.onSnapshot((snapshot) => {
      if (snapshot.exists) {
        const data = snapshot.data();

        if (data) {
          // Detectar paro total de emergencia
          if (
            data.accion === 244 &&
            data.act === "recOK" &&
            data.kind === 4 &&
            data.val === 0
          ) {
            console.log(`[WaterTankMonitor] PARO TOTAL DETECTADO - Parámetros:`, data);
            handleEmergencyStop();
          }
        }
      }
    });

    // Guardar la referencia del listener global
    unsubscribeRefs.current['emergencyStopListener'] = unsubscribe;

    return () => {
      if (unsubscribeRefs.current['emergencyStopListener']) {
        unsubscribeRefs.current['emergencyStopListener']();
        delete unsubscribeRefs.current['emergencyStopListener'];
      }
    };
  }, [usuario, currentMac, canIdIrrigation, handleEmergencyStop]);

  // Log de estado actual para debugging
  useEffect(() => {
    if (monitoredTanks.length > 0) {
      console.log(`[WaterTankMonitor] Tanques actualmente monitoreados:`,
        monitoredTanks.map(t => ({ id: t.id, name: t.name, action: t.action }))
      );
    }
  }, [monitoredTanks]);

  // Este componente no renderiza nada visible
  return null;
});

export default WaterTankMonitor;
