import React from 'react'
import { I<PERSON><PERSON>utton, Tooltip } from '@material-ui/core'
import { withRouter } from 'react-router-dom';
import { ALL_PATHS } from '../../../../constants/routerConst';


const AIChatbotIcon = (props) => {
	const sparklesIcon = '/sparkles-svgrepo-com-Mini.svg';
	const handleSVGPress = () => {
		// Navegar a la configuración del módulo
		const route = `${ALL_PATHS.PATH_ChatbotJapy}`;
		props.history.push(route);
	};

  return (
	<div>
		<Tooltip title="chat Japy IA" arrow>
        <IconButton onClick={handleSVGPress}>
          <img style={{width: '100%'}} src={sparklesIcon} alt="SVG Icon" />
        </IconButton>
        </Tooltip>
	</div>
  )
}

const ChatbotJapyWithRouter = withRouter(AIChatbotIcon);

export { ChatbotJapyWithRouter as AIChatbotIcon };
export default ChatbotJapyWithRouter;
