import React, { useContext, useState } from 'react';
import japyIcon from "../../../assets/openFresa.png";
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  Paper,
  TextField,
  IconButton,
  InputAdornment,
  Button,
  Fade,
  Slide,
  Collapse,
} from '@material-ui/core';
import {
  makeStyles,
  alpha
} from '@material-ui/core/styles';
import {
  Send,
  ExpandMore,
  Eco,
  Timeline,
} from '@material-ui/icons';
import { UserContext } from '../../../context/UserProvider';
import { MultiCropContext } from '../../../context/MultiCropContext/MultiCropContext';
import { SelectSensors } from '../ChatBotForm/SelectSensors';

const useStyles = makeStyles((theme) => ({
  root: {
    minHeight: '100vh',
    backgroundColor: theme.palette.background.default,
    padding: theme.spacing(3),
  },
  mainContainer: {
    maxWidth: 1200,
    margin: '0 auto',
  },
  headerCard: {
    background: 'white',
    borderRadius: 10,
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
    marginBottom: theme.spacing(3),
    overflow: 'visible',
  },
  cropSelector: {
    minWidth: 250,
    '& .MuiOutlinedInput-root': {
      borderRadius: 10,
      background: 'white',
      '&:hover': {
        background: '#f8f9fa',
      },
    },
  },
  cropInfoCard: {
    background: theme.palette.primary.main,
    color: 'white',
    borderRadius: 10,
    marginTop: theme.spacing(2),
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
  },
  sensorSection: {
    marginTop: theme.spacing(3),
  },
  sensorCard: {
    borderRadius: 10,
    background: 'white',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
  },
  chatContainer: {
    background: 'white',
    borderRadius: 10,
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
    marginTop: theme.spacing(3),
  },
  chatHeader: {
    background: theme.palette.primary.main,
    color: 'white',
    padding: theme.spacing(2),
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
  },
  chatMessages: {
    height: 400,
    overflowY: 'auto',
    padding: theme.spacing(2),
    background: '#f8f9fa',
  },
  messageBot: {
    background: '#e3f2fd',
    borderRadius: '15px 15px 15px 4px',
    padding: theme.spacing(1.5),
    marginBottom: theme.spacing(1),
    maxWidth: '80%',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  messageUser: {
    background: theme.palette.primary.main,
    color: 'white',
    borderRadius: '15px 15px 4px 15px',
    padding: theme.spacing(1.5),
    marginBottom: theme.spacing(1),
    maxWidth: '80%',
    marginLeft: 'auto',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  chatInput: {
    padding: theme.spacing(2),
    background: 'white',
    borderTop: '1px solid #e0e0e0',
  },
  inputField: {
    '& .MuiOutlinedInput-root': {
      borderRadius: 10,
      background: '#f8f9fa',
      '&:hover': {
        background: '#f0f0f0',
      },
      '&.Mui-focused': {
        background: 'white',
      },
    },
  },
  analyzeButton: {
    backgroundColor: theme.palette.primary.main,
    color: 'white',
    borderRadius: 10,
    padding: theme.spacing(1.5, 3),
    textTransform: 'none',
    fontWeight: 600,
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
    '&:hover': {
      backgroundColor: '#4a9029',
    },
  },
  iconAvatar: {
    backgroundColor: theme.palette.primary.main,
    width: 40,
    height: 40,
  },
  expandButton: {
    transition: 'transform 0.3s ease',
  },
  expandButtonRotated: {
    transform: 'rotate(180deg)',
  },
  infoChip: {
    margin: theme.spacing(0.5),
    background: alpha('#ffffff', 0.2),
    color: 'white',
    '& .MuiChip-icon': {
      color: 'white',
    },
  },
}));

export const ChatbotJapy = () => {
  const classes = useStyles();
  const { usuario } = useContext(UserContext);
  const {
    namesOfCrops,
    crops,
    weeksOfCrops,
    daysOfCrops,
    placeOfCrops,
    variantsOfCrop,
    phenoStages,
    indoorSystems,
    soilTypes,
  } = useContext(MultiCropContext);

  const [cropSelected, setCropSelected] = useState(0);
  const [expandedSensors, setExpandedSensors] = useState(false);
  const [expandedCropInfo, setExpandedCropInfo] = useState(true);
  const [messages, setMessages] = useState([
    {
      type: 'bot',
      content: '¡Hola! Soy Japy, tu asistente de agricultura inteligente. Selecciona un cultivo y sus sensores para comenzar el análisis.',
      timestamp: new Date(),
    },
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [loadingAnalysis, setLoadingAnalysis] = useState(false);
  const [synchronized, setSynchronized] = useState(false);

  const weekAndDay = `Semana ${weeksOfCrops[cropSelected]} y Día ${daysOfCrops[cropSelected]}`;
  const envCrop = placeOfCrops[cropSelected] === "Exterior" ? soilTypes[cropSelected] : indoorSystems[cropSelected];

  const handleChangeCrop = (event) => {
    setCropSelected(event.target.value);
    setSynchronized(false);
    // Agregar mensaje del bot sobre el cambio de cultivo
    const botMessage = {
      type: 'bot',
      content: `Has seleccionado el cultivo: ${namesOfCrops[event.target.value]}. Ahora puedes configurar los sensores y realizar un análisis.`,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, botMessage]);
  };

  const handleSendMessage = () => {
    if (newMessage.trim() === '') return;

    const userMessage = {
      type: 'user',
      content: newMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');

    // Simular respuesta del bot (aquí iría la llamada a la API)
    setTimeout(() => {
      const botResponse = {
        type: 'bot',
        content: 'Mensaje recibido. Esta funcionalidad se conectará con la API de análisis.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, botResponse]);
    }, 1000);
  };

  const handleAnalyzeCrop = async () => {
    if (!synchronized) {
      const errorMessage = {
        type: 'bot',
        content: 'Por favor, primero sincroniza los sensores antes de realizar el análisis.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
      return;
    }

    setLoadingAnalysis(true);

    const analysisMessage = {
      type: 'user',
      content: `Solicitar análisis del cultivo: ${namesOfCrops[cropSelected]}`,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, analysisMessage]);

    // Simular llamada a la API
    setTimeout(() => {
      const botResponse = {
        type: 'bot',
        content: `Análisis iniciado para el cultivo ${namesOfCrops[cropSelected]}. Los datos de los sensores están siendo procesados...`,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, botResponse]);
      setLoadingAnalysis(false);
    }, 2000);
  };

  const toggleSensorsExpansion = () => {
    setExpandedSensors(!expandedSensors);
  };

  const toggleCropInfoExpansion = () => {
    setExpandedCropInfo(!expandedCropInfo);
  };

  return (
    <Box className={classes.root}>
        <Box className={classes.mainContainer}>
          {/* Header con selector de cultivo */}
          <Fade in timeout={800}>
            <Card className={classes.headerCard}>
              <CardContent>
                <Grid container spacing={3} alignItems="center">
                  <Grid item>
                    <Avatar className={classes.iconAvatar}>
                      <img style={{width: '100%'}} src={japyIcon} alt="Japy Icon" />
                    </Avatar>
                  </Grid>
                  <Grid item xs>
                    <Typography variant="h5" gutterBottom>
                      Japy - Asistente de Agricultura Inteligente
                    </Typography>
                    <Typography variant="body1" color="textSecondary">
                      Analiza tus cultivos con inteligencia artificial
                    </Typography>
                  </Grid>
                  <Grid item>
                    <FormControl variant="outlined" className={classes.cropSelector}>
                      <InputLabel>Seleccionar Cultivo</InputLabel>
                      <Select
                        value={cropSelected}
                        onChange={handleChangeCrop}
                        label="Seleccionar Cultivo"
                        // startAdornment={
                        //   <InputAdornment position="start">
                        //     <Agriculture color="primary" />
                        //   </InputAdornment>
                        // }
                      >
                        {namesOfCrops.map((item, index) => (
                          <MenuItem value={index} key={index}>
                            {item}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Fade>

          {/* Información del cultivo */}
          <Slide direction="up" in timeout={1000}>
            <Card className={classes.cropInfoCard}>
              <CardContent>
                <Grid container alignItems="center" justifyContent="space-between">
                  <Grid item>
                    <Typography variant="h6" gutterBottom>
                      Información del Cultivo
                    </Typography>
                  </Grid>
                  <Grid item>
                    <IconButton
                      onClick={toggleCropInfoExpansion}
                      className={`${classes.expandButton} ${
                        expandedCropInfo ? classes.expandButtonRotated : ''
                      }`}
                      style={{ color: 'white' }}
                    >
                      <ExpandMore />
                    </IconButton>
                  </Grid>
                </Grid>

                <Collapse in={expandedCropInfo}>
                  <Grid container spacing={2} style={{ marginTop: 8 }}>
                    <Grid item xs={12} sm={6} md={4}>
                      <Chip
                        icon={<Eco />}
                        label={`${crops[cropSelected]} ${variantsOfCrop[cropSelected]}`}
                        className={classes.infoChip}
                        variant="outlined"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Chip
                        icon={<Timeline />}
                        label={`${phenoStages[cropSelected]}`}
                        className={classes.infoChip}
                        variant="outlined"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Chip
                        // icon={<Analytics />}
                        label={weekAndDay}
                        className={classes.infoChip}
                        variant="outlined"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Chip
                        label={`Entorno: ${placeOfCrops[cropSelected]}`}
                        className={classes.infoChip}
                        variant="outlined"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Chip
                        label={`${placeOfCrops[cropSelected] === "Exterior" ? "Suelo" : "Sistema"}: ${envCrop}`}
                        className={classes.infoChip}
                        variant="outlined"
                      />
                    </Grid>
                  </Grid>
                </Collapse>
              </CardContent>
            </Card>
          </Slide>

          {/* Sección de sensores */}
          <Slide direction="up" in timeout={1200}>
            <Card className={classes.sensorCard}>
              <CardContent>
                <Grid container alignItems="center" justifyContent="space-between">
                  <Grid item>
                    <Typography variant="h6" gutterBottom>
                      {/* <Sensors style={{ marginRight: 8, verticalAlign: 'middle' }} /> */}
                      Configuración de Sensores
                    </Typography>
                  </Grid>
                  <Grid item>
                    <IconButton
                      onClick={toggleSensorsExpansion}
                      className={`${classes.expandButton} ${
                        expandedSensors ? classes.expandButtonRotated : ''
                      }`}
                    >
                      <ExpandMore />
                    </IconButton>
                  </Grid>
                </Grid>

                <Collapse in={expandedSensors}>
                  <Box style={{ marginTop: 16 }}>
                    <SelectSensors
                      indexOfCrop={cropSelected}
                      username={usuario.username}
                      confirmSync={() => setSynchronized(true)}
                      loadingChat={loadingAnalysis}
                    />
                  </Box>
                </Collapse>
              </CardContent>
            </Card>
          </Slide>

          {/* Botón de análisis */}
          <Fade in timeout={1400}>
            <Box textAlign="center" style={{ margin: '24px 0' }}>
              <Button
                variant="contained"
                size="large"
                className={classes.analyzeButton}
                onClick={handleAnalyzeCrop}
                disabled={loadingAnalysis}
                // startIcon={
                //   loadingAnalysis ? (
                //     <CircularProgress size={20} color="inherit" />
                //   ) : (
                //     // <Analytics />
					
                //   )
                // }
              >
                {loadingAnalysis ? 'Analizando...' : 'Analizar Cultivo'}
              </Button>
            </Box>
          </Fade>

          {/* Chatbot */}
          <Slide direction="up" in timeout={1600}>
            <Paper className={classes.chatContainer}>
              {/* Header del chat */}
              <Box className={classes.chatHeader}>
                <Avatar style={{ marginRight: 12 }}>
                  {/* <SmartToy /> */}
                </Avatar>
                <Typography variant="h6">
                  Chat con Japy
                </Typography>
              </Box>

              {/* Mensajes */}
              <Box className={classes.chatMessages}>
                {messages.map((message, index) => (
                  <Fade in key={index} timeout={500} style={{ transitionDelay: `${index * 100}ms` }}>
                    <Box
                      className={
                        message.type === 'bot' ? classes.messageBot : classes.messageUser
                      }
                    >
                      <Typography variant="body1">
                        {message.content}
                      </Typography>
                      <Typography
                        variant="caption"
                        style={{
                          opacity: 0.7,
                          marginTop: 4,
                          display: 'block',
                          fontSize: '0.75rem',
                        }}
                      >
                        {message.timestamp.toLocaleTimeString()}
                      </Typography>
                    </Box>
                  </Fade>
                ))}
              </Box>

              {/* Input del chat */}
              <Box className={classes.chatInput}>
                <TextField
                  fullWidth
                  variant="outlined"
                  placeholder="Escribe tu mensaje aquí..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  className={classes.inputField}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          color="primary"
                          onClick={handleSendMessage}
                          disabled={newMessage.trim() === ''}
                        >
                          <Send />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
            </Paper>
          </Slide>
        </Box>
      </Box>
  );
};

