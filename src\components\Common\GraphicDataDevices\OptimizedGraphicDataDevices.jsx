import React, { useState, useEffect, useContext, useCallback, useMemo } from "react";
import Highcharts from "highcharts/highstock";
import HighchartsReact from "highcharts-react-official";

// Material UI
import {
  InputLabel,
  MenuItem,
  FormControl,
  Select,
  Button,
  CircularProgress,
  Grid,
  Input,
  ListItemText,
  Typography,
  makeStyles
} from "@material-ui/core";
import { green } from "@material-ui/core/colors";

// Context
import { UserContext } from "../../../context/UserProvider";

// Components
import TransitionAlerts from "../TransitionAlerts";
import { HIGHCHART_OPT_SPANISH, ALL_KINDS } from "../../../constants/globalConst";
import Loader from "../Loader";
import OptimizedTableDataChartWithDetails from "./OptimizedTableDataChartWithDetails";
import ResponsiveDateTimeInterval from "../ResponsiveDateTimeInterval";
import { MultiCropContext } from "../../../context/MultiCropContext/MultiCropContext";

// Initialize Highcharts options
Highcharts.setOptions(HIGHCHART_OPT_SPANISH);

// Constants
const ITEM_HEIGHT = 100;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};


const defaultOpenAlert = { open: null, type: null, tittle: null, txt: null };

const useStyles = makeStyles(() => ({
  buttonProgress: {
    color: green[500],
    marginLeft: 12,
  },
}));

const OptimizedGraphicDataDevices = ({
  inputDevices = [],
  outDevices = [],
  manualDevices = [],
  getDataFromDB,
  seriesModifier,
  setSeriesModifier,
  component,
}) => {
  const classes = useStyles();
  const { usuario, getTimezoneOffsetInSeconds } = useContext(UserContext);
   const { namesOfCrops,allTilesTest, hiddenCards,  } = useContext(MultiCropContext);
   const [selectedTab, setSelectedTab] = useState(0);
  const exportDataUrl = process.env.REACT_APP_EXPORT_HISTORICAL_DATA;

  // State management
  const [openAlert, setOpenAlert] = useState(defaultOpenAlert);
  const [propSelectName, setPropSelectName] = useState([]);
  const [series, setSeries] = useState([]);
  const [dataTable, setDataTable] = useState([]);
  const [enableLoader, setEnableLoader] = useState(false);
  const [optionsHighChart, setOptionsHighChart] = useState();
  const [nColor, setNColor] = useState(Math.floor(Math.random() * 10));
  const [loadingExport, setLoadingExport] = useState(false);
  const [uidsSelected, setUidsSelected] = useState([]);
  const [flgChangeDate, setFlgChangeDate] = useState(false);

  // Initialize dates
  const diaActual = useMemo(() => new Date(), []);
  const diaAyer = useMemo(() => {
    const date = new Date(diaActual);
    date.setDate(date.getDate() - 7);
    return date;
  }, [diaActual]);

  const [fechaIni, setFechaIni] = useState(diaAyer.toISOString());
  const [fechaFin, setFechaFin] = useState(diaActual.toISOString());

  // Color management
  const aumentaColor = useCallback(() => {
    setNColor(prevColor => prevColor < 9 ? prevColor + 1 : 0);
  }, []);

  // Set fullFill color for charts
  const setFullfillColor = useCallback((ini, fin) => {
    const js = {
      linearGradient: {
        x1: 0,
        y1: 0,
        x2: 0,
        y2: 1,
      },
      stops: [
        [
          0,
          Highcharts.color(Highcharts.getOptions().colors[nColor])
            .setOpacity(ini)
            .get("rgba"),
        ],
        [
          1,
          Highcharts.color(Highcharts.getOptions().colors[nColor])
            .setOpacity(fin)
            .get("rgba"),
        ],
      ],
    };
    aumentaColor();
    return js;
  }, [nColor, aumentaColor]);

  // Line type setters
  const setDigitalLine = useCallback((sensor, chartData) => {
    return {
      name: sensor.label,
      label: sensor.label,
      data: chartData,
      step: "left",
      yAxis: 0,
      uid: sensor.uid,
      color: Highcharts.color(Highcharts.getOptions().colors[nColor])
        .setOpacity(1)
        .get("rgba"),
      type: "area",
      fillColor: setFullfillColor(0.2, 0),
    };
  }, [nColor, setFullfillColor]);

  const setAnalogLine = useCallback((sensor, chartData) => {
    return {
      name: sensor.label,
      label: sensor.label,
      data: chartData,
      yAxis: 1,
      uid: sensor.uid,
      color: Highcharts.color(Highcharts.getOptions().colors[nColor])
        .setOpacity(1)
        .get("rgba"),
      type: "areaspline",
      fillColor: setFullfillColor(0.2, 0),
    };
  }, [nColor, setFullfillColor]);

  // Update table data
  const updateTable = useCallback((chart) => {
    if (!chart || !chart.series) return;

    const updatedData = chart.series.map((serie) => {
      // Obtener los datos completos (timestamp y valor)
      const rawData = serie.options.data || [];

      return {
        name: serie.userOptions.label,
        label: serie.userOptions.label,
        color: serie.color,
        points: serie.processedYData,
        step: serie.userOptions.step,
        yAxis: serie.userOptions.yAxis,
        uid: serie.userOptions.uid,
        rawData: rawData, // Incluir los datos completos con timestamp
      };
    });

    setDataTable(updatedData);
  }, []);

  // Alert management
  const close = useCallback(() => {
    setOpenAlert(defaultOpenAlert);
  }, []);

  const settingAlarm = useCallback((uid) => {
    let alertjs = {
      open: true,
      type: "error",
      tittle: "Error",
      txt: "No hay datos",
    };

    const isSensor = inputDevices.find((item) => item.uid === uid);
    if (isSensor) {
      alertjs.txt = `No hay datos registrados del sensor ${isSensor.label}`;
    } else {
      const isSalida = outDevices.find((item) => item.uid === uid);
      if (isSalida) {
        alertjs.txt = `No hay datos registrados de la salida ${isSalida.label}`;
      }
    }

    setOpenAlert(alertjs);
  }, [inputDevices, outDevices]);

  // Series management
  const addSerieToGraph = useCallback((newSerie) => {
    setSeries(prevSeries => [...prevSeries, newSerie]);
  }, []);

  const deleteSerieFromGraph = useCallback((label) => {
    setSeries(prevSeries => prevSeries.filter(serie => serie.label !== label));
  }, []);

  const deleteTableCol = useCallback((label) => {
    setDataTable(prevData => prevData.filter(serie => serie.label !== label));
  }, []);

  const deleteDeviceAllStates = useCallback((label) => {
    deleteSerieFromGraph(label);
    deleteTableCol(label);
    setPropSelectName(prevNames => prevNames.filter(item => item !== label));
  }, [deleteSerieFromGraph, deleteTableCol]);

  // Data fetching
  const getDataset = useCallback(async (user, uid, fIni, fFin, tipo) => {
    return await getDataFromDB(user, uid, fIni, fFin, tipo);
  }, [getDataFromDB]);

  const getSerie = useCallback(async (sensor, type) => {
    setOpenAlert(defaultOpenAlert);

    // Check if already selected
    const foundSelected = propSelectName.find(item => item === sensor.label);
    if (foundSelected) {
      deleteDeviceAllStates(sensor.label);
      return;
    }

    // Determine type
    const tipo = type === "manual" ? "manual" : "modulo";

    // Fetch data
    setEnableLoader(true);
    try {
      const chartData = await getDataset(
        usuario.username,
        sensor.uid,
        fechaIni,
        fechaFin,
        tipo
      );

      setUidsSelected(prevValues => [...prevValues, sensor.uid]);

      if (chartData.length === 0) {
        deleteDeviceAllStates(sensor.label);
        settingAlarm(sensor.uid);
        return;
      }

      let newSerie;

      if (type === "out") {
        // Process digital output data
        const processedData = chartData.map(point => {
          const [timestamp, value] = point;
          return [timestamp, Math.trunc(parseInt(value))];
        });

        newSerie = setDigitalLine(sensor, processedData);
      } else if (type === "in") {
        // Process input data
        const dataSensor = sensor.uid.split("@");

        if (dataSensor[2] === ALL_KINDS.IN_SWITCH) {
          newSerie = setDigitalLine(sensor, chartData);
        } else {
          newSerie = setAnalogLine(sensor, chartData);
        }
      } else {
        // Manual data
        newSerie = setAnalogLine(sensor, chartData);
      }

      addSerieToGraph(newSerie);
    } catch (error) {
      console.error("Error fetching data:", error);
      setOpenAlert({
        open: true,
        type: "error",
        tittle: "Error",
        txt: "Error al obtener datos del dispositivo"
      });
    } finally {
      setEnableLoader(false);
    }
  }, [
    propSelectName,
    deleteDeviceAllStates,
    getDataset,
    usuario,
    fechaIni,
    fechaFin,
    settingAlarm,
    setDigitalLine,
    setAnalogLine,
    addSerieToGraph
  ]);

  // Handle series modification from external source
  useEffect(() => {
    if (!seriesModifier || !seriesModifier.action) return;

    const serieToModify = series.find(
      item => item.label === seriesModifier.formData?.name
    );

    if (!serieToModify) return;

    const insertArray_BtoA_OrderByTime = (arrayA, arrayB) => {
      const cloneArrayA = [...arrayA];
      const indexTime = 0;
      const isLargeNumber = element => element[indexTime] > arrayB[indexTime];

      let index = cloneArrayA.findIndex(isLargeNumber);
      if (index === -1) {
        index = cloneArrayA.length;
      }

      cloneArrayA.splice(index, 0, arrayB);
      return cloneArrayA;
    };

    let editSeries = [...series];

    switch (seriesModifier.action) {
      case "add": {
        const formData = seriesModifier.formData;
        const dataToMakeAction = [formData.date, parseFloat(formData.value)];
        const newDataSerie = insertArray_BtoA_OrderByTime(
          serieToModify.data,
          dataToMakeAction
        );

        const updatedSerie = { ...serieToModify, data: newDataSerie };
        editSeries = series.map(item =>
          item.label === updatedSerie.label ? updatedSerie : item
        );
        break;
      }

      case "deleteByTime": {
        const formData = seriesModifier.formData;
        const checkedEraseAll = formData.checkedEraseAll;

        if (checkedEraseAll) {
          // Delete entire series
          editSeries = series.filter(item => item.label !== formData.name);
        } else {
          // Delete between times
          const timeInit = formData.fromDateDelete;
          const timeFin = formData.date;
          const indexTime = 0;

          const isNotBetweenTimes = item =>
            !(item[indexTime] >= timeInit && item[indexTime] <= timeFin);

          const newDataSerie = serieToModify.data.filter(isNotBetweenTimes);
          const updatedSerie = { ...serieToModify, data: newDataSerie };

          editSeries = series.map(item =>
            item.label === updatedSerie.label ? updatedSerie : item
          );
        }
        break;
      }

      case "update": {
        const { lastData, formData } = seriesModifier;

        // First delete the old data point
        const indexTime = 0;
        const indexValue = 1;
        const isNotTheDataSelectedToDelete = item =>
          !(item[indexTime] === lastData.date && item[indexValue] === parseFloat(lastData.value));

        const filteredData = serieToModify.data.filter(isNotTheDataSelectedToDelete);
        const tempSerie = { ...serieToModify, data: filteredData };

        // Then add the new data point
        const dataToMakeAction = [formData.date, parseFloat(formData.value)];
        const newDataSerie = insertArray_BtoA_OrderByTime(
          tempSerie.data,
          dataToMakeAction
        );

        const updatedSerie = { ...tempSerie, data: newDataSerie };
        editSeries = series.map(item =>
          item.label === updatedSerie.label ? updatedSerie : item
        );
        break;
      }

      case "delete": {
        const { formData } = seriesModifier;
        const indexTime = 0;
        const indexValue = 1;

        const isNotTheDataSelectedToDelete = item =>
          !(item[indexTime] === formData.date && item[indexValue] === parseFloat(formData.value));

        const newDataSerie = serieToModify.data.filter(isNotTheDataSelectedToDelete);
        const updatedSerie = { ...serieToModify, data: newDataSerie };

        editSeries = series.map(item =>
          item.label === updatedSerie.label ? updatedSerie : item
        );
        break;
      }

      default:
        break;
    }

    setSeries(editSeries);
  }, [seriesModifier, series]);

  // Update Highcharts options when series change
  useEffect(() => {
    const optionsChart = {
      time: {
        timezoneOffset: getTimezoneOffsetInSeconds()
      },
      chart: {
        zoomType: "xy",
        events: {
          redraw: function() {
            updateTable(this);
          },
        },
      },
      subtitle: {
        text: "Seleccione una sección para realizar zoom",
      },
      title: {
        text: "",
      },
      xAxis: {
        crosshair: true,
        type: "datetime",
        labels: {
          rotation: 315,
          overflow: "justify",
          style: {
            fontSize: "15px",
          },
        },
      },
      yAxis: [
        {
          title: {
            text: "", // Digital
          },
          opposite: false,
        },
        {
          title: {
            text: "", // Entradas
            style: { color: "green" },
          },
          opposite: true,
          labels: {
            format: "{value} ",
          },
        },
      ],
      credits: {
        enabled: false,
      },
      navigation: {
        menuItemStyle: {
          fontSize: "15px"
        }
      },
      exporting: {
        filename: `Graficas_Comparativa_Param`,
        sourceWidth: 1280,
        sourceHeight: 720,
        chartOptions: { subtitle: null },
      },
      series: series || [],
    };

    setOptionsHighChart(optionsChart);
  }, [series, getTimezoneOffsetInSeconds, updateTable]);

  // Update propSelectName when series change
  useEffect(() => {
    const newSelect = series.map(item => item.label);
    setPropSelectName(newSelect);
  }, [series]);

  // Reset data when date changes
  useEffect(() => {
    if (flgChangeDate && propSelectName.length > 0) {
      setPropSelectName([]);
      setDataTable([]);
      setSeries([]);
      setFlgChangeDate(false);
    }
  }, [flgChangeDate, propSelectName]);

  // Estado para almacenar los dispositivos filtrados por cultivo
  const [filteredInputDevices, setFilteredInputDevices] = useState([]);
  const [filteredOutDevices, setFilteredOutDevices] = useState([]);
  const [filteredManualDevices, setFilteredManualDevices] = useState([]);

  useEffect(() => {
    if(allTilesTest.length !== 0) {
      const uids = [];
      const devices = [];
      allTilesTest[selectedTab].forEach(element => {
        if(element.length > 0) {
          element.forEach((item) => {
            if(item.kind !== "ghost" && item.kind !== "nodeLoraWan") {
              uids.push(item.uid);
              devices.push(item.name);

            } else if(item.kind === "nodeLoraWan") {
              devices.push(item.uid);
            }
          })
        }
      })

      // Filtrar dispositivos basándose en los nombres asociados al cultivo seleccionado
      const filteredInput = inputDevices.filter(device =>
        devices.includes(device.label) || devices.includes(device.name) || devices.includes(device.uidBase)
      );
      const filteredOut = outDevices.filter(device =>
        devices.includes(device.label) || devices.includes(device.name)
      );
      const filteredManual = manualDevices.filter(device =>
        devices.includes(device.label) || devices.includes(device.name) || devices.includes(device.value)
      );

      setFilteredInputDevices(filteredInput);
      setFilteredOutDevices(filteredOut);
      setFilteredManualDevices(filteredManual);
    } else {
      // Si no hay datos de cultivo, limpiar los dispositivos filtrados
      setFilteredInputDevices([]);
      setFilteredOutDevices([]);
      setFilteredManualDevices([]);
    }
  }, [allTilesTest, selectedTab, inputDevices, outDevices, manualDevices]);

  const handleTabChange = (event) => {
    setSelectedTab(event.target.value);
  };
  

  // Event handlers
  const handleChange = useCallback((event) => {
    setPropSelectName(event.target.value);
  }, []);

  const handleDateChange = useCallback((date, id) => {
    if (!date || !date._d) return;

    const newDate = new Date(date._d.getTime()).toISOString();

    if (id === "time-picker-ini") {
      if (newDate !== fechaIni) {
        setFlgChangeDate(true);
      }
      setFechaIni(newDate);
    } else if (id === "time-picker-fin") {
      if (newDate !== fechaFin) {
        setFlgChangeDate(true);
      }
      setFechaFin(newDate);
    }
  }, [fechaIni, fechaFin]);

  const handleExportData = useCallback(async () => {
    if (propSelectName.length === 0 || loadingExport) return;

    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const data = {
      username: usuario.username,
      uids_array: uidsSelected,
      startDate: fechaIni,
      endDate: fechaFin,
      uids_titles: propSelectName,
      timeZone: timeZone
    };

    try {
      setLoadingExport(true);
      const dataToSend = JSON.stringify(data);

      const response = await fetch(exportDataUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: dataToSend,
      });

      if (!response.ok) throw new Error('Network response was not ok.');

      // Get response as Blob
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);

      // Create temporary link and simulate click to download
      const a = document.createElement('a');
      a.href = downloadUrl;

      // Customize filename for download if needed
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `${usuario.username}_datos.csv`; // Default name
      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename="?(.*?)"?($|;)/);
        if (fileNameMatch) {
          filename = fileNameMatch[1];
        }
      }

      a.download = filename;
      document.body.appendChild(a);
      a.click();

      // Clean up
      document.body.removeChild(a);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Error exporting data:', error);
      setOpenAlert({
        open: true,
        type: "error",
        tittle: "Error",
        txt: "Error al exportar datos"
      });
    } finally {
      setLoadingExport(false);
    }
  }, [
    propSelectName,
    loadingExport,
    usuario.username,
    uidsSelected,
    fechaIni,
    fechaFin
  ]);

  // Render device menu items
  const renderDeviceMenuItems = useMemo(() => {
    const outDeviceItems = filteredOutDevices?.map((s, i) => (
      <MenuItem
        key={`out-${s.uid}-${i}`}
        value={s.label}
        onClick={() => getSerie(s, "out")}
      >
        {s.label}
      </MenuItem>
    )) || [];

    const inputDeviceItems = filteredInputDevices?.map((s, i) => (
      <MenuItem
        key={`in-${s.uid}-${i}`}
        value={s.label}
        onClick={() => getSerie(s, "in")}
      >
        {s.label}
      </MenuItem>
    )) || [];

    const manualDeviceItems = filteredManualDevices?.map((s, i) => (
      <MenuItem
        key={`manual-${s.uid}-${i}`}
        value={s.label}
        onClick={() => getSerie(s, "manual")}
      >
        {s.label}
      </MenuItem>
    )) || [];

    return {
      outDeviceItems,
      inputDeviceItems,
      manualDeviceItems
    };
  }, [filteredOutDevices, filteredInputDevices, filteredManualDevices, getSerie]);

  return (
    <div>
      <Grid
        container
        spacing={3}
        direction="row"
        justifyContent="space-between"
        alignItems="flex-end"
      >
        <Grid item xs={1}>
          <Typography variant="h5" gutterBottom>
            Desde
          </Typography>
        </Grid>
        <Grid item xs={5}>
          <ResponsiveDateTimeInterval
            id="time-picker-ini"
            selectedDate={fechaIni}
            handleDateChange={handleDateChange}
          />
        </Grid>
        <Grid item xs={1}>
          <Typography variant="h5" gutterBottom>
            hasta
          </Typography>
        </Grid>
        <Grid item xs={5}>
          <ResponsiveDateTimeInterval
            id="time-picker-fin"
            selectedDate={fechaFin}
            handleDateChange={handleDateChange}
          />
        </Grid>

        <Grid item xs={12} md={3}>
          {namesOfCrops.length > 0 ? 
            <FormControl fullWidth>
              <InputLabel>Cultivo:</InputLabel>
              <Select value={selectedTab} onChange={handleTabChange}>
                {namesOfCrops?.map((tab,index) => (
                  <MenuItem key={index} value={index}>{tab}</MenuItem>
                ))}
              </Select>
            </FormControl>
            :
            <Typography>
              Cargando lista de cultivos...
            </Typography>
          }
        </Grid>

        <Grid item xs={12} md={8}>
          {(filteredOutDevices?.length > 0 || filteredInputDevices?.length > 0 || filteredManualDevices?.length > 0) ? (
            <FormControl style={{ minWidth: 120 }}>
              <InputLabel htmlFor="grouped-select">Dispositivos</InputLabel>

              <Select
                defaultValue=""
                id="grouped-select"
                multiple
                value={propSelectName}
                onChange={handleChange}
                input={<Input />}
                MenuProps={MenuProps}
              >
                {(filteredInputDevices.length === 0 && filteredOutDevices.length === 0 && filteredManualDevices.length === 0) && (
                  // <MenuItem value="" disabled>
                  //   Cargando lista...
                  // </MenuItem>
                  <Typography> 
                    No hay dispositivos asociados al cultivo seleccionado
                  </Typography>
                )}

                {filteredOutDevices.length > 0 && (
                  <MenuItem key="HeadOut" value="Salidas" disabled>
                    <ListItemText>Salidas</ListItemText>
                  </MenuItem>
                )}
                {renderDeviceMenuItems.outDeviceItems}

                {filteredInputDevices.length > 0 && (
                  <MenuItem key="HeadIn" value="Entradas" disabled>
                    <ListItemText>Entradas</ListItemText>
                  </MenuItem>
                )}
                {renderDeviceMenuItems.inputDeviceItems}

                {filteredManualDevices.length > 0 && (
                  <MenuItem key="HeadManual" value="Manuales" disabled>
                    <ListItemText>Bitácora (Manuales)</ListItemText>
                  </MenuItem>
                )}
                {renderDeviceMenuItems.manualDeviceItems}
              </Select>
            </FormControl>
          ) : (
            // <div style={{ padding: '16px', color: '#666', fontStyle: 'italic' }}>
            //   No hay dispositivos asociados al cultivo seleccionado
            // </div>
            <Typography> 
              Cargando lista de dispositivos...
            </Typography>
          )}
        </Grid>

        <Grid item container xs={12} justifyContent="flex-end" alignItems="flex-end">
          <Button
            variant="contained"
            color="primary"
            disabled={propSelectName.length === 0 || loadingExport}
            onClick={handleExportData}
          >
            Exportar datos
          </Button>
          {loadingExport && <CircularProgress size={35} className={classes.buttonProgress} />}
        </Grid>

        <Grid item xs={6}>
          <Loader loading={enableLoader} />
        </Grid>

        <Grid item xs></Grid>

        <Grid item xs>
          {component && component.alert}
        </Grid>
      </Grid>

      <Grid container spacing={1} direction="column">
        <Grid item xs>
          <TransitionAlerts data={openAlert} onClick={close} enableHead />
        </Grid>

        <Grid item xs>
          {series.length > 0 && (
            <HighchartsReact
              highcharts={Highcharts}
              options={optionsHighChart}
            />
          )}
        </Grid>

        <Grid item xs>
          <OptimizedTableDataChartWithDetails dataTable={dataTable} />
        </Grid>
      </Grid>
    </div>
  );
};

export default OptimizedGraphicDataDevices;
export { OptimizedGraphicDataDevices };
