import React, { useContext, useEffect, useState } from 'react'
import { Button, Grid, IconButton, makeStyles, Tab, Tabs, Tooltip } from '@material-ui/core'
import { AddCircle, Autorenew, DeleteForever, Edit, Refresh } from '@material-ui/icons';
import { NewCropConfigPopup } from './NewCropConfigPopup';
import { MultiCropContext } from '../../../../context/MultiCropContext/MultiCropContext';
import { EditCropConfig } from './EditCropConfig';
import { UserContext } from '../../../../context/UserProvider';
import moment from 'moment-timezone';
import { DeleteCropWindow } from './DeleteCropWindow';

const useStyles = makeStyles((theme) => ({
	root: {
	flexGrow: 1,
	width: '50%',
	//backgroundColor: theme.palette.background.paper,
	position: 'relative',
	},
	tabsContainer: {
	display: 'flex',
	alignItems: 'center',
	},
	addButton: {
	position: 'absolute',
	right: 0,
	zIndex: 1,
	},
	tabs: {
	flexGrow: 1,
	marginRight: theme.spacing(6), // Deja espacio para el botón
	},
	button: {
		margin: theme.spacing(1),
	},
}));


export const MulticropHeader = ({setOpenBackdrop,setLoadingUpdateData,loadingUpdateData}) => {
	const classes = useStyles();
	const {userTimezone, numberOfCans, currentMac} = useContext(UserContext);
	const { namesOfCrops,tabValue,setTabValue,actualNumberOfCrops,placeOfCrops,crops,
		variantsOfCrop,phenoStages,datesOfStages,
		setWeeksOfCrops,setDaysOfCrops, openConfigNewCrop,openEditWindow, setOpenEditWindow,
		setOpenConfigNewCrop } = useContext(MultiCropContext);
	const [totalCrops, setTotalCrops] = useState(["Default"])
	// const [openConfigNewCrop, setOpenConfigNewCrop] = useState(false);
	const [openDeleteWindow, setOpenDeleteWindow] = useState(false);

	const handleCloseConfigNewCrop = () => { setOpenConfigNewCrop(false) }
	const handleCloseEditCrop = () => { setOpenEditWindow(false) }
	const handleCloseDeleteCrop = () => { setOpenDeleteWindow(false) }

	// Función para calcular semanas y días desde dateOfStage
	const calculateWeeksAndDays = (dateOfStage, timezone) => {
		if (!dateOfStage || !timezone) return { weeks: 0, days: 1 };

		try {
			// Crear fechas solo con año/mes/día, sin considerar la hora
			const stageDate = moment.tz(dateOfStage, timezone).startOf('day');
			const currentDate = moment.tz(timezone).startOf('day');

			// Calcular la diferencia total en días
			const totalDays = currentDate.diff(stageDate, 'days');

			if (totalDays < 0) {
				// Si la fecha de etapa es futura, mostrar 0 semanas y 1 día
				return { weeks: 0, days: 1 };
			}

			// Calcular semanas completas y días restantes
			const weeks = Math.floor(totalDays / 7);
			const days = (totalDays % 7) + 1; // +1 porque empezamos desde el día 1, no 0

			return { weeks, days: days > 8 ? 1 : days };
		} catch (error) {
			console.error("Error calculando semanas y días:", error);
			return { weeks: 0, days: 1 };
		}
	};

	// Calcular semanas y días actuales para la pestaña seleccionada
	const currentStageDate = datesOfStages.length > 0 ? datesOfStages[tabValue] : null;
	const { weeks: currentWeeks, days: currentDays } = calculateWeeksAndDays(currentStageDate, userTimezone);

	const weekToShow = currentWeeks;
	const dayToShow = currentDays;
	const placeToShow = placeOfCrops.length > 0 ? placeOfCrops[tabValue] : "-";
	const cropToShow = crops.length > 0 ?  `${crops[tabValue]} ${variantsOfCrop[tabValue]}` : "-";

	function a11yProps(index) {
		return {
			id: `crop-${index}`,
			'aria-controls': `crop-${index}`,
		};
	}

	const handleCropTabChange = (event, newValue) => {
		setTabValue(newValue)
		// El efecto en MulticropProvider se encargará de guardar el valor en localStorage
	}

	const handleUpdateData = async() => {
		setLoadingUpdateData(true);
		console.log("Entre al handleUpdateData")
		setOpenBackdrop(true)
	}
	// useEffect para actualizar semanas y días basándose en dateOfStage
	useEffect(() => {
		try {
			let lastKnownDate = null;

			const updateWeeksAndDays = () => {
				if (datesOfStages.length > 0 && userTimezone) {
					const arrayOfDays = [];
					const arrayOfWeeks = [];

					datesOfStages.forEach((dateOfStage) => {
						const { weeks, days } = calculateWeeksAndDays(dateOfStage, userTimezone);
						arrayOfWeeks.push(String(weeks));
						arrayOfDays.push(String(days));
					});

					setWeeksOfCrops([...arrayOfWeeks]);
					setDaysOfCrops([...arrayOfDays]);
				}
			};

			const checkForNewDay = () => {
				const currentDate = moment.tz(userTimezone).format('YYYY-MM-DD');

				if (lastKnownDate === null) {
					// Primera ejecución, guardar la fecha actual
					lastKnownDate = currentDate;
					updateWeeksAndDays();
				} else if (lastKnownDate !== currentDate) {
					// Se detectó un nuevo día, actualizar
					console.log(`Nuevo día detectado: ${lastKnownDate} -> ${currentDate}`);
					lastKnownDate = currentDate;
					updateWeeksAndDays();
				}
			};

			// Actualizar inmediatamente
			checkForNewDay();

			// Configurar un intervalo para verificar cada 10 minutos si cambió el día
			const intervalId = setInterval(() => {
				checkForNewDay();
			}, 600000); // 10 minutos en milisegundos

			// Limpiar el intervalo al desmontar el componente
			return () => clearInterval(intervalId);
		} catch (error) {
			console.error("Error al actualizar semanas y días:", error);
		}
	}, [datesOfStages, userTimezone, setWeeksOfCrops, setDaysOfCrops])


  return (
	<div>
	<Grid container justifyContent="flex-start">
      <div className={classes.root}>
        <div className={classes.tabsContainer}>
          <Tabs
            value={tabValue}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
            onChange={handleCropTabChange}
            className={classes.tabs}
          >
            {namesOfCrops?.map((cropName, index) => (
              <Tab label={cropName} {...a11yProps(index)} key={`crop${index}`} />
            ))}
          </Tabs>
		  <Tooltip title="Añadir Cultivo" arrow>
          <IconButton
            color="primary"
            aria-label="add new Crop"
            onClick={() => {setOpenConfigNewCrop(true)}}
            className={classes.addButton}
			disabled={actualNumberOfCrops === 15}
          >
            <AddCircle />
          </IconButton>
		  </Tooltip>
        </div>
      </div>
	  {/* <div style={{
        display: "flex",            // Para alinear elementos en la misma fila
        alignItems: "center",       // Centra verticalmente
        marginTop: "10px",
      	}}> */}
	  	{/* <Tooltip title="Editar Cultivo" arrow>
			<IconButton
				className={classes.button}
				onClick={() => { setOpenEditWindow(true)}}
				//startIcon={<Edit />}
				//endIcon={<Edit />}
			>
				<Edit />
			</IconButton>
		</Tooltip>

		{actualNumberOfCrops > 1 && (
			<Tooltip title="Eliminar Cultivo" arrow>
				<IconButton
					className={classes.button}
					onClick={() => { setOpenDeleteWindow(true)}}
					//startIcon={<Edit />}
					//endIcon={<Edit />}

				>
					<DeleteForever />
				</IconButton>
			</Tooltip>
		)} */}

		{/* <label className='h5' style={{ marginRight: "10px"}}>
			{`${phenoStages[tabValue]} | Semana: ${weekToShow}, Día: ${dayToShow} |`}
		</label>
		<label className='h5'>
			{`${cropToShow} | ${placeToShow} `}
		</label> */}

		{/* <div style={{ marginLeft: "auto" }}>
			<Tooltip title="Actualizar datos del dashboard" arrow>
			<Button
				variant="contained"
				color="primary"
				onClick={handleUpdateData}
				startIcon={<Autorenew />}
				disabled={loadingUpdateData}
			>

			</Button>
			</Tooltip>
		</div> */}
	  {/* </div> */}

	  <NewCropConfigPopup
	  openPopup={openConfigNewCrop}
	  handleClosePopup={handleCloseConfigNewCrop}
	  totalCrops={totalCrops}
	  setTotalCrops={setTotalCrops}
	  setOpenConfigNewCrop={setOpenConfigNewCrop}
	  />

	  <EditCropConfig
	  openPopup={openEditWindow}
	  handleClosePopup={handleCloseEditCrop}
	  />

	  <DeleteCropWindow
	  openPopup={openDeleteWindow}
	  handleClosePopup={handleCloseDeleteCrop}
	  />
    </Grid>
	<div style={{
        display: "flex",            // Para alinear elementos en la misma fila
        alignItems: "center",       // Centra verticalmente
        marginTop: "10px",
      	}}>
	  	<Tooltip title="Editar Cultivo" arrow>
			<IconButton
				className={classes.button}
				onClick={() => { setOpenEditWindow(true)}}
				//startIcon={<Edit />}
				//endIcon={<Edit />}
			>
				<Edit />
			</IconButton>
		</Tooltip>

		{actualNumberOfCrops > 1 && (
			<Tooltip title="Eliminar Cultivo" arrow>
				<IconButton
					className={classes.button}
					onClick={() => { setOpenDeleteWindow(true)}}
					//startIcon={<Edit />}
					//endIcon={<Edit />}

				>
					<DeleteForever />
				</IconButton>
			</Tooltip>
		)}

		<label className='h5' style={{ marginRight: "10px"}}>
			{`${phenoStages[tabValue]} | Semana: ${weekToShow}, Día: ${dayToShow} |`}
		</label>
		<label className='h5'>
			{`${cropToShow} | ${placeToShow} `}
		</label>

		{(currentMac !== "" && numberOfCans.length !== 0) && (
			<div style={{ marginLeft: "auto" }}>
				<Tooltip title="Actualizar datos del dashboard" arrow>
				<Button
					variant="contained"
					color="primary"
					onClick={handleUpdateData}
					startIcon={<Autorenew />}
					disabled={loadingUpdateData}
				>
					{/* Texto u otro contenido dentro del botón */}
				</Button>
				</Tooltip>
			</div>
		)}
	  </div>
	  </div>
  )
}
