import React, { useContext, useEffect, useState, useCallback, useRef } from 'react'
import { Button, CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle, Divider, FormControl, Grid, InputLabel, makeStyles, MenuItem, Select, TextField, Typography, Snackbar, Box } from '@material-ui/core'
import { HighchartsReact } from 'highcharts-react-official';
import Highcharts from "highcharts/highstock";
import { KeyboardDatePicker, MuiPickersUtilsProvider } from '@material-ui/pickers';
import DateFnsUtils from '@date-io/date-fns';
import { setYear } from 'date-fns';
import { Alert } from '@material-ui/lab';
import { CheckCircle, Error } from '@material-ui/icons';
import { UserContext } from '../../../../context/UserProvider';
import { db, functions } from '../../../../config/firebase';

// Arreglo de colores para las gráficas
const COLORS = ['#1a8cff', '#e35454', '#9ccc65', '#ffa726', '#c62828', '#ce93d8', '#26a69a', '#5c6bc0', '#ffd600']

// Opciones de tiempo
const opcionesTiempo = [
  { value: 'mes', label: 'Mes' },
  { value: 'semana', label: 'Semana' },
  { value: 'año', label: 'Año' }
];

const opcionesMeses = [
  'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
  'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
];

const useStyles = makeStyles((theme) => ({
  redHeader: {
    // fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,

  },
  boxSection: {
    border: '1.5px dashed #ffffffff',
    borderRadius: 10,
    marginBottom: 18,
    padding: '18px 12px',
    background: '#ffffffff',
  },
  // dividerDashed: {
  //   borderTop: '1.5px dashed #000000ff',
  //   margin: '22px 0',
  // },
  centeredButton: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 14,
    marginBottom: 10,
  },
  codeFont: {
    fontSize: '1.1em',
  },
  graficaArea: {
    border: '1.5px dashed #ffffffff',
    borderRadius: 10,
    minHeight: 210,
    width: '100%',
    marginTop: 12,
    background: '#ffffffff',
    // display: 'flex',
    // alignItems: 'center',
    // justifyContent: 'center',
  },
}));

// Define constantes para calcular alturas
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;

const MenuProps = {
  PaperProps: {
    style: {
      // Máximo de 4.5 ítems visibles
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      // Ancho fijo
      width: 300,
    },
  },
};

export const ConfigIndividualPowerConsum = ({open,togglesNames,togglesIds,volt,onChangeVolt,onClose}) => {
  const classes = useStyles();
  const { usuario, currentMac, canIdIrrigation, getTimezoneOffsetInSeconds } = useContext(UserContext);

  const [voltage, setVoltage] = useState(Number(volt))
	const [tiempo, setTiempo] = useState('mes');
	const [mesSeleccionado, setMesSeleccionado] = useState('Junio');
  const [selectedDevices, setSelectedDevices] = useState([])
	const [datos, setDatos] = useState(null);
  const [isSavingVoltage, setIsSavingVoltage] = useState(false)
  const [showAlert, setShowAlert] = useState(false)
  const [alertMessage, setAlertMessage] = useState('')
  const [alertType, setAlertType] = useState('success')
  // Estado para almacenar los datos de cada dispositivo individualmente
  const [deviceDataCache, setDeviceDataCache] = useState({});
  const [loadingDevices, setLoadingDevices] = useState({}) // loading individual
  const [startDate, setStartDate] = useState(() => {
    let today = new Date();
    let oneWeek = new Date();
    oneWeek.setDate(today.getDate() - 7);
    return oneWeek;
  });
  // const [startDate, setStartDate] = useState(new Date() - 7 * 24 * 60 * 60 * 1000);
  const [endDate, setEndDate] = useState(new Date());
  const [yearOptions, setYearOptions] = useState([]);
  const [yearSelected, setYearSelected] = useState(new Date().getFullYear() - 1);
  const currentMonth = new Date().getMonth() // 0 = Enero
  const mesesHabilitados = opcionesMeses.slice(0, currentMonth + 1) // Solo meses del año en curso
  // bandera para saltar el primer fetch después de cambiar tiempo
const skipFetchAfterPeriodChange = useRef(false);

  // Opciones para los años
  useEffect(() => {
    const currentYear = new Date().getFullYear()
    setYearOptions([currentYear - 1, currentYear - 2])
  }, [])

  // Actualizar voltage cuando cambie el prop volt
  useEffect(() => {
    setVoltage(Number(volt) || 0);
  }, [volt])

  // Estado para controlar si se está limpiando por cambio de tiempo
  const [isClearing, setIsClearing] = useState(false);

  // Limpiar datos y dispositivos seleccionados cuando cambie el tiempo
  useEffect(() => {
    skipFetchAfterPeriodChange.current = true;
    setSelectedDevices([]);
    setDatos(null);
    setDeviceDataCache({});
    setLoadingDevices({});
  }, [tiempo])

  // Helper para identificar la consulta en caché
  const cacheKey = useCallback((index) => {
    if (tiempo === 'semana') return `${index}_${startDate}_${endDate}_semana`
    if (tiempo === 'mes') return `${index}_${mesSeleccionado}_${yearSelected}_mes`
    if (tiempo === 'año') return `${index}_${yearSelected}_año`
    return `${index}_${tiempo}`
  }, [tiempo, startDate, endDate, mesSeleccionado, yearSelected])

  const getDataFromBQ = useCallback(async (uid, fIni, fFin) => {
    console.log("Esto es uid:", uid);
    console.log("Esto es fIni:", fIni);
    console.log("Esto es fFin:", fFin);
    let valArray = [];
    const getDataSetValues = functions.httpsCallable("getDataSetValues");
    const values = await getDataSetValues({
      user: usuario.username,
      uid: uid.trim(),
      dateStart: fIni,
      dateFinish: fFin,
    });

    let data = values.data.result[0];
    for (let i in data) {
      let newEntry = [Date.parse(data[i].timestamp.value), data[i].val];
      valArray.push(newEntry);
    }
    
    return valArray;
  }, [usuario.username]);


  // Lógica para pedir los datos sólo cuando sea necesario
  const fetchDeviceData = useCallback(async (index) => {
    setLoadingDevices(ld => ({ ...ld, [index]: true }))
    try {
      // Aquí defines las fechas igual que antes
      let fIni, fFin
      // "semana"
      if (tiempo === 'semana') {
        fIni = startDate.toISOString()
        fFin = endDate.toISOString()
      }
      // "mes"
      else if (tiempo === 'mes') {
        const monthIndex = opcionesMeses.indexOf(mesSeleccionado)
        const year = new Date().getFullYear()
        fIni = new Date(year, monthIndex, 1).toISOString()
        // Ultimo día del mes seleccionado:
        fFin = new Date(year, monthIndex + 1, 0, 23, 59, 59).toISOString()
      }
      // "año"
      else if (tiempo === 'año') {
        fIni = new Date(yearSelected, 0, 1).toISOString()
        fFin = new Date(yearSelected, 11, 31, 23, 59, 59).toISOString()
      }

      // Petición a la nube, igual que tu lógica anterior
      const deviceId = togglesIds[index]
      const uid = `${currentMac}@${canIdIrrigation}@4@${deviceId}`
      
      const values = await getDataFromBQ( uid, fIni, fFin )
      if(values?.length > 0) {
        let dataArray = []
        if (values?.length) {
          // dataArray = values.map(d => [Date.parse(d.timestamp.value), d.val])
          dataArray = [...values];
        }
        console.log("Esto es values:",values)
        setDeviceDataCache(cache => ({
          ...cache,
          [cacheKey(index)]: {
            name: togglesNames[index],
            data: dataArray,
          }
        }))
        setAlertType('success')
        setAlertMessage('Datos cargados correctamente')
        setShowAlert(true)
      } else {
        setAlertType('error')
        setAlertMessage('No hay datos disponibles para el dispositivo seleccionado en la fecha seleccionada')
        setShowAlert(true)
      }
    } catch (err) {
      setDeviceDataCache(cache => ({
        ...cache,
        [cacheKey(index)]: {
          name: togglesNames[index],
          data: []
        }
      }))
    }
    setLoadingDevices(ld => {
      const updated = { ...ld }
      delete updated[index]
      return updated
    })
  }, [currentMac, canIdIrrigation, togglesIds, togglesNames, tiempo, mesSeleccionado, yearSelected, startDate, endDate, cacheKey, getDataFromBQ])

  // Pedir datos cuando cambian los dispositivos seleccionados o los filtros
  useEffect(() => {
    if (skipFetchAfterPeriodChange.current) {
      skipFetchAfterPeriodChange.current = false;
      return; // salto la primera ejecución tras el cambio de periodo
    }

    if (selectedDevices.length === 0) {
      setDatos(null)
      return
    }

    selectedDevices.forEach(index => {
      const key = cacheKey(index)
      if (!deviceDataCache[key]) {
        fetchDeviceData(index)
      }
    })
    setDatos(null)
  }, [selectedDevices, mesSeleccionado, yearSelected, startDate, endDate])

  // Construye las series para la gráfica
  useEffect(() => {
    if (selectedDevices.length === 0) {
      setDatos(null)
      return
    }
    const series = selectedDevices.map((index, i) => {
      const key = cacheKey(index)
      const device = deviceDataCache[key]
      return device
        ? {
          name: device.name,
          data: device.data,
          color: COLORS[i % COLORS.length], // Color único para cada serie
        }
        : null
    }).filter(Boolean)
    console.log("Esto es series:",series)
    if (series.length) setDatos({ series })
    else setDatos(null)
  }, [selectedDevices, deviceDataCache, cacheKey])
  

  const handleVoltageChange = (e) => {
    setVoltage(e.target.value);
    // if(e.target.value >= 0) {
    //   setVoltage(Number(e.target.value));
    // } else {
    //   setVoltage(0);
    // }
    
  }

  const saveVoltage = async () => {
    if (!usuario.username || currentMac === ""  || canIdIrrigation === "") return;
    if(voltage === "" || Number(voltage) < 0) return
    setIsSavingVoltage(true);

    try {
      const result = await onChangeVolt(Number(voltage));

      if (result && result.success) {
        setAlertType('success');
        setAlertMessage('Voltaje guardado exitosamente');
        setShowAlert(true);
      } else {
        setAlertType('error');
        setAlertMessage('Error al guardar el voltaje');
        setShowAlert(true);
      }
    } catch (error) {
      console.error('Error al guardar voltaje:', error);
      setAlertType('error');
      setAlertMessage('Error al guardar el voltaje');
      setShowAlert(true);
    } finally {
      setIsSavingVoltage(false);
    }
  }

  const handleStartDateChange = (date) => {
    setStartDate(date ? new Date(date) : null);
  };

  const handleEndDateChange = (date) => {
    setEndDate(date ? new Date(date) : null);
  };

  // Opciones de la gráfica
  const chartOptions = {
    chart: {
      // type: 'column', // Cambiar a gráfica de barras
      zoomType: 'xy', // Habilitar zoom horizontal y vertical
      // panning: {
      //   enabled: true,
      //   type: 'xy'
      // },
      // panKey: 'shift',
      resetZoomButton: {
        theme: {
          fill: 'white',
          stroke: 'silver',
          r: 0,
          states: {
            hover: {
              fill: '#41739D',
              style: {
                color: 'white'
              }
            }
          }
        },
        position: {
          align: 'right',
          verticalAlign: 'top',
          x: -10,
          y: 10
        }
      }
    },
    time: {
      // timezoneOffset: new Date().getTimezoneOffset(),
      timezoneOffset: getTimezoneOffsetInSeconds()
    },
    title: {
      text: 'Corriente histórico',
      style: {
        fontSize: '16px',
        fontWeight: 'bold'
      }
    },
    subtitle: {
      text: 'Seleccione una sección para realizar zoom',
      style: {
        fontSize: '12px',
        color: '#666'
      }
    },
    xAxis: {
      type: 'datetime',
      title: { text: 'Tiempo' },
      crosshair: true
    },
    yAxis: {
      title: { text: 'Corriente (A)' },
      min: 0,
      crosshair: true
    },
    credits: {
      enabled: false,
    },
    tooltip: {
      shared: true,
      crosshairs: true,
      xDateFormat: '%Y-%m-%d %H:%M:%S',
      headerFormat: '<b>{point.x:%Y-%m-%d %H:%M:%S}</b><br/>',
      // pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f} A</b><br/>'
    },
    legend: {
      enabled: true,
      layout: 'horizontal',
      align: 'center',
      verticalAlign: 'bottom'
    },
    plotOptions: {
      column: {
        pointPadding: 0.1,
        borderWidth: 0,
        groupPadding: 0.1,
        shadow: false,
        dataLabels: {
          enabled: false
        }
      }
    },
    series: datos?.series || []
  }
  


  return (
     <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <DialogContent>
        {/* --- Sección Editar Voltaje --- */}
        <div className={classes.boxSection}>
          <Typography variant="h5" className={classes.redHeader}>Voltaje</Typography>
          <Grid container spacing={2} alignItems="center" justifyContent="center">
            <Grid item xs={12} md={7} style={{ textAlign: 'center', justifyContent: 'center', display: 'flex' }}>
              
              <TextField
                variant="outlined"
                type="number"
                value={voltage}
                onChange={handleVoltageChange}
                // inputProps={{ style: { textAlign: 'center', width: 70} }}
                style={{ margin: '0 10px' }}
                size='small'
              />
              <span>V</span>
            </Grid>
            <Grid item xs={12} className={classes.centeredButton}>
              <Box position="relative" display="inline-flex">
                <Button
                  variant="contained"
                  color="primary"
                  style={{ minWidth: 180 }}
                  onClick={saveVoltage}
                  disabled={isSavingVoltage || voltage === "" || Number(voltage) < 0}
                >
                  {isSavingVoltage ? 'Guardando...' : 'Editar Voltaje'}
                </Button>
                {isSavingVoltage && (
                  <CircularProgress
                    size={24}
                    style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      marginTop: -12,
                      marginLeft: -12,
                    }}
                  />
                )}
              </Box>
            </Grid>
          </Grid>
        </div>

        <Divider />

        {/* --- Sección Visualización de Gráficas --- */}
        <div className={classes.boxSection}>
          <Typography variant="h5" className={classes.redHeader}>Histórico de corriente</Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <FormControl variant="outlined" fullWidth margin="dense">
                <InputLabel>Consultar por</InputLabel>
                <Select
                  value={tiempo}
                  onChange={e => setTiempo(e.target.value)}
                  label="Consultar por"
                >
                  {opcionesTiempo.map(op => (
                    <MenuItem key={op.value} value={op.value}>{op.label}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            {tiempo === 'mes' && (
              <Grid item xs={12} md={4}>
                <FormControl variant="outlined" fullWidth margin="dense">
                  <InputLabel>Mes</InputLabel>
                  <Select
                    value={mesSeleccionado}
                    onChange={e => setMesSeleccionado(e.target.value)}
                    label="Mes"
                  >
                    {mesesHabilitados.map(mes => (
                      <MenuItem key={mes} value={mes}>{mes}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}

            {tiempo === 'año' && (
              <Grid item xs={12} md={4}>
                <FormControl variant="outlined" fullWidth margin="dense">
                  <InputLabel>Año</InputLabel>
                  <Select
                    value={yearSelected}
                    onChange={e => setYearSelected(e.target.value)}
                    label="Año"
                  >
                    {yearOptions?.map((year) => (
                      <MenuItem key={year} value={year}>{year}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}

            
            {tiempo === 'semana' && (
              <Grid item xs={12} md={8}>
                <MuiPickersUtilsProvider utils={DateFnsUtils}>
                  <Grid 
                    container 
                    direction="row" // Horizontal
                    justifyContent="flex-start" // Alinea a la izquierda
                    alignItems="center" // Alinea verticalmente al centro
                    spacing={2} // Espacio entre pickers
                  >
                    <Grid item>
                      <KeyboardDatePicker
                        disableToolbar
                        variant="inline"
                        format="dd/MM/yyyy"
                        margin="normal"
                        id="date-picker-start"
                        label="Desde"
                        value={startDate}
                        maxDate={endDate instanceof Date
                          ? new Date(endDate.getTime() - 24 * 60 * 60 * 1000)
                          : undefined
                        } // Un día antes de endDate
                        onChange={handleStartDateChange}
                        KeyboardButtonProps={{
                          'aria-label': 'change date',
                        }}
                      />
                    </Grid>
                    <Grid item>
                      <KeyboardDatePicker
                        disableToolbar
                        variant="inline"
                        format="dd/MM/yyyy"
                        margin="normal"
                        id="date-picker-end"
                        label="Hasta"
                        value={endDate}
                        minDate={startDate instanceof Date
                          ? new Date(startDate.getTime() + 24 * 60 * 60 * 1000)
                          : undefined
                        } // Un día después de startDate
                        maxDate={new Date()} // Hoy
                        onChange={handleEndDateChange}
                        KeyboardButtonProps={{
                          'aria-label': 'change date',
                        }}
                      />
                    </Grid>
                  </Grid>
                </MuiPickersUtilsProvider>
              </Grid>
            )}

            <Grid item xs={12} md={12} style={{ display: 'flex', alignItems: 'center' }}>
              <FormControl variant="outlined" fullWidth margin="dense">
                <InputLabel>Dispositivos</InputLabel>
                <Select
                  multiple
                  value={selectedDevices}
                  onChange={e => setSelectedDevices(e.target.value)}
                  label="Dispositivos"
                  MenuProps={MenuProps}
                  renderValue={selected =>
                    selected.map(index => togglesNames[index]).join(',')
                  }
                >
                  {togglesNames.length === 0 && (
                    <MenuItem value="" disabled>
                      No hay dispositivos para mostrar
                    </MenuItem>
                  )}
                  {togglesNames?.map((toggle,index) => (
                    <MenuItem key={index} value={index}>
                      {toggle}
                      {loadingDevices[index] && (
                        <CircularProgress size={18} style={{ marginLeft: 8 }} />
                      )}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
           
            <Grid item xs={12}>
              <div className={classes.graficaArea}>
                {!datos && selectedDevices.length === 0 && (
                  <Typography className={classes.codeFont}>
                    Selecciona uno o más dispositivos para ver la gráfica
                  </Typography>
                )}
                {!datos && selectedDevices.length > 0 && Object.values(deviceDataCache)
                  .filter(d => d && d.data && d.data.length === 0).length > 0 && (
                  <Typography className={classes.codeFont}>
                    No hay datos disponibles para los dispositivos seleccionados
                  </Typography>
                )}
                {datos && <HighchartsReact highcharts={Highcharts} options={chartOptions} />}
              </div>
            </Grid>
          </Grid>
        </div>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="secondary" variant="outlined">
          Cerrar
        </Button>
      </DialogActions>

      {/* Snackbar para mostrar alertas de éxito/error */}
      <Snackbar
        open={showAlert}
        autoHideDuration={4000}
        onClose={() => setShowAlert(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowAlert(false)}
          severity={alertType}
          variant="filled"
          icon={alertType === 'success' ? <CheckCircle /> : <Error />}
        >
          {alertMessage}
        </Alert>
      </Snackbar>
    </Dialog>
  )
}
